"use client"
import { SetStateAction, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { ArrowDown, ArrowUp, ArrowUpDown, ChevronLeft, ChevronRight, Search, ExternalLink } from "lucide-react"
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area"

import { AdvancedStats } from "@/types/player"

export function OffensiveStatsTable({
  players,
  selectedPlayerId,
  onPlayerSelect
}: {
  players: Array<{
    id: string | number;
    name: string;
    position: string;
    playerIndex: number;
    team: string;
    conference?: string;
    class?: string;
    advancedStats?: AdvancedStats;
    transferPortal?: boolean;
    status?: string;
    active?: boolean;
  }>;
  selectedPlayerId: string | number | null;
  onPlayerSelect: (player: any) => void;
}) {
  const [sortField, setSortField] = useState("playerIndex") // Default sort by player index
  const [sortDirection, setSortDirection] = useState("desc") // Default sort direction descending
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  const handleSort = (field: SetStateAction<string>) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("desc") // Default to descending for new sort fields
    }
  }

  const getSortIcon = (field: string) => {
    if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />
    return sortDirection === "asc" ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
  }

  // Function to determine the color class based on the player index value
  const getIndexColorClass = (index: number) => {
    if (index >= 90) return "text-green-600 dark:text-green-400"
    if (index >= 80) return "text-emerald-600 dark:text-emerald-400"
    if (index >= 70) return "text-blue-600 dark:text-blue-400"
    if (index >= 60) return "text-yellow-600 dark:text-yellow-400"
    if (index >= 50) return "text-orange-600 dark:text-orange-400"
    return "text-red-600 dark:text-red-400"
  }

  // Function to determine the progress color based on the player index value
  const getProgressColorClass = (index: number) => {
    if (index >= 90) return "bg-green-600"
    if (index >= 80) return "bg-emerald-600"
    if (index >= 70) return "bg-blue-600"
    if (index >= 60) return "bg-yellow-600"
    if (index >= 50) return "bg-orange-600"
    return "bg-red-600"
  }

  // Filter players based on search term
  const filteredPlayers = players.filter((player) => player.name.toLowerCase().includes(searchTerm.toLowerCase()))

  // Sort players
  const sortedPlayers = [...filteredPlayers].sort((a, b) => {
    let aValue: any, bValue: any;

    if (sortField === "name") {
      aValue = a.name;
      bValue = b.name;
    } else if (sortField === "position") {
      aValue = a.position;
      bValue = b.position;
    } else if (sortField === "class") {
      // Handle undefined/null values for class
      aValue = a.class || "";
      bValue = b.class || "";
    } else if (sortField === "playerIndex") {
              aValue = a.playerIndex;
        bValue = b.playerIndex;
    } else if (sortField.startsWith("advancedStats.")) {
      const statField = sortField.split(".")[1] as keyof AdvancedStats;
      aValue = a.advancedStats?.[statField] || 0;
      bValue = b.advancedStats?.[statField] || 0;
    } else {
      // For any other fields, use type assertion to access properties safely
      const field = sortField as keyof typeof a;
      aValue = a[field] || "";  // Add fallback for undefined values
      bValue = b[field] || "";  // Add fallback for undefined values
    }

    if (typeof aValue === "string") {
      return sortDirection === "asc" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    }

    return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
  })

  // Pagination
  const totalPages = Math.ceil(sortedPlayers.length / itemsPerPage)
  const paginatedPlayers = sortedPlayers.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  // Format percentage values
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  }

  // Format decimal values
  const formatDecimal = (value: number) => {
    return value.toFixed(1);
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search players..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value)
              setCurrentPage(1)
            }}
          />
        </div>
      </div>

      <ScrollArea className="w-full">
        <div className="rounded-md border border-border">
          <table className="min-w-max w-full border-collapse">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2 font-semibold sticky left-0 bg-background z-10">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("name")}
                    className="flex items-center gap-1 p-0 h-auto font-medium"
                  >
                    Player {getSortIcon("name")}
                  </Button>
                </th>
                <th className="p-2 text-left">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("class")}
                    className="flex items-center gap-1 p-0 h-auto font-medium"
                  >
                    Class {getSortIcon("class")}
                  </Button>
                </th>
                <th className="p-2 text-left">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("position")}
                    className="flex items-center gap-1 p-0 h-auto font-medium"
                  >
                    Pos {getSortIcon("position")}
                  </Button>
                </th>
                <th className="p-2 text-left">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("team")}
                    className="flex items-center gap-1 p-0 h-auto font-medium"
                  >
                    Team {getSortIcon("team")}
                  </Button>
                </th>
                <th className="p-2 text-left">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("playerIndex")}
                    className="flex items-center gap-1 p-0 h-auto font-medium"
                  >
                    Player Index {getSortIcon("playerIndex")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                                  <Button
                  variant="ghost"
                  onClick={() => handleSort("advancedStats.ts_pct")}
                  className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                >
                  TS% {getSortIcon("advancedStats.ts_pct")}
                </Button>
              </th>
              <th className="p-2 text-right">
                <Button
                  variant="ghost"
                  onClick={() => handleSort("advancedStats.efg_pct")}
                  className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                >
                  eFG% {getSortIcon("advancedStats.efg_pct")}
                </Button>
              </th>
              <th className="p-2 text-right">
                <Button
                  variant="ghost"
                  onClick={() => handleSort("advancedStats.total_s_pct")}
                  className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                >
                  Total S% {getSortIcon("advancedStats.total_s_pct")}
                </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("advancedStats.orb")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    ORB% {getSortIcon("advancedStats.orb")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("advancedStats.ast")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    AST% {getSortIcon("advancedStats.ast")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("advancedStats.tov")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    TOV% {getSortIcon("advancedStats.tov")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("advancedStats.usg")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    USG% {getSortIcon("advancedStats.usg")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("advancedStats.ppr")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    PPR {getSortIcon("advancedStats.ppr")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("advancedStats.pps")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    PPS {getSortIcon("advancedStats.pps")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("advancedStats.ortg")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    ORtg {getSortIcon("advancedStats.ortg")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("advancedStats.ftrate")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    FTRATE {getSortIcon("advancedStats.ftrate")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("advancedStats.fic")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    FIC {getSortIcon("advancedStats.fic")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("advancedStats.per")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    PER {getSortIcon("advancedStats.per")}
                  </Button>
                </th>
              </tr>
            </thead>
            <tbody>
              {paginatedPlayers.length > 0 ? (
                paginatedPlayers.map((player) => (
                  <tr
                    key={player.id}
                    className={`border-b cursor-pointer ${selectedPlayerId === player.id ? "bg-muted" : ""}`}
                    onClick={() => onPlayerSelect(player)}
                  >
                    <td className="p-2 font-medium sticky left-0 bg-background z-10">
                      <div className="flex items-center gap-2">
                        <span className="text-primary hover:underline flex items-center gap-1 cursor-pointer">
                          {player.name}
                          <ExternalLink className="h-3 w-3 opacity-70" />
                        </span>
                      </div>
                      {!player.active && <span className="ml-2 text-xs text-destructive">(Inactive)</span>}
                    </td>
                    <td className="p-2">
                      {player.class ? (
                        <Badge variant="outline" className="text-xs">
                          {player.class}
                        </Badge>
                      ) : '-'}
                    </td>
                    <td className="p-2">{player.position}</td>
                    <td className="p-2">{player.team}</td>
                    <td className="p-2">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                                                                  <span className={`font-bold ${getIndexColorClass(player.playerIndex)}`}>
                  {player.playerIndex === 0 ? "NR" : player.playerIndex.toFixed(1)}
                </span>
                      </div>
                      <Progress
                                                    value={player.playerIndex}
                        max={100}
                        className="h-2 w-24"
                                                    indicatorClassName={getProgressColorClass(player.playerIndex)}
                      />
                      </div>
                    </td>
                    <td className="p-2 text-right">{formatPercentage(player.advancedStats?.ts_pct || 0)}</td>
                    <td className="p-2 text-right">{formatPercentage(player.advancedStats?.efg_pct || 0)}</td>
                    <td className="p-2 text-right">{formatPercentage(player.advancedStats?.total_s_pct || 0)}</td>
                    <td className="p-2 text-right">{formatPercentage(player.advancedStats?.orb_pct || 0)}</td>
                    <td className="p-2 text-right">{formatPercentage(player.advancedStats?.ast_pct || 0)}</td>
                    <td className="p-2 text-right">{formatPercentage(player.advancedStats?.tov_pct || 0)}</td>
                    <td className="p-2 text-right">{formatPercentage(player.advancedStats?.usg_pct || 0)}</td>
                    <td className="p-2 text-right">{formatDecimal(player.advancedStats?.ppr || 0)}</td>
                    <td className="p-2 text-right">{formatDecimal(player.advancedStats?.pps || 0)}</td>
                    <td className="p-2 text-right">{formatDecimal(player.advancedStats?.ortg || 0)}</td>
                    <td className="p-2 text-right">{formatDecimal(player.advancedStats?.ftr || 0)}</td>
                    <td className="p-2 text-right">{formatDecimal(player.advancedStats?.fic || 0)}</td>
                    <td className="p-2 text-right">{formatDecimal(player.advancedStats?.per || 0)}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={17} className="h-24 text-center p-2">
                    No players found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {(currentPage - 1) * itemsPerPage + 1}-
            {Math.min(currentPage * itemsPerPage, filteredPlayers.length)} of {filteredPlayers.length}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Previous Page</span>
            </Button>
            <div className="text-sm">
              Page {currentPage} of {totalPages}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Next Page</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
