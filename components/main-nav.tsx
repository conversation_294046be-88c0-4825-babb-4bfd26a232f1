"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { LogIn, LogOut, BarChart2, Settings } from "lucide-react"
import { usePathname } from "next/navigation"
import { useAuth } from "./auth-provider"
import { useIndex } from "./index-provider"

export function MainNav() {
  const pathname = usePathname()
  const { isAuthenticated, logout } = useAuth()
  const { useCustomIndex, indexName } = useIndex()

  // Don't show the nav on auth pages
  if (pathname === "/login" || pathname === "/register" || pathname === "/forgot-password") {
    return null
  }

  return (
    <header className="sticky top-0 z-40 bg-background">
      <div className="border-b border-border">
        <div className="container flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8 mx-auto max-w-full">
          <div className="flex items-center gap-2">
            <Link href="/" className="flex items-center gap-2">
              <Image
                src="/logo-trans.png"
                alt="PlayVision Logo"
                width={100}
                height={100}
                className="h-8 w-auto"
              />
              <h1 className="text-xl font-bold">PlayVision Player Portal</h1>
            </Link>
          </div>
          <div className="flex items-center gap-4">
            {useCustomIndex && (
              <div className="hidden md:block text-sm text-muted-foreground truncate max-w-[220px]">Using: {indexName}</div>
            )}
            <Button variant="default" size="sm" asChild>
              <Link href="/compare">
                <BarChart2 className="h-4 w-4 mr-2" />
                Compare Players
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/create-index">
                <Settings className="h-4 w-4 mr-2" />
                Create Index
              </Link>
            </Button>

            {isAuthenticated ? (
              // Show logout button when authenticated
              <Button variant="outline" size="sm" onClick={logout}>
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
          ) : (
              // Show login button when not authenticated
              <Button variant="outline" size="sm" asChild>
                <Link href="/login">
                  <LogIn className="h-4 w-4 mr-2" />
                  Sign In
                </Link>
              </Button>
          )}
          </div>
        </div>
      </div>
    </header>
  )
}
