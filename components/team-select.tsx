"use client"

import { useState, useEffect, useRef } from "react"
import { Input } from "@/components/ui/input"
import { Search, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { getPlayerData } from "@/utils/data-transform"
// Using native overflow container for scroll behavior
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"

interface TeamSelectProps {
  value: string[]
  onValueChange: (value: string[]) => void
}

export function TeamSelect({ value, onValueChange }: TeamSelectProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [teams, setTeams] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)
  const areaRef = useRef<HTMLDivElement>(null)
  const [isScrolling, setIsScrolling] = useState(false)

  // Load teams on component mount
  useEffect(() => {
    const loadTeams = async () => {
      try {
        setIsLoading(true)
        const response = await getPlayerData()

        // Extract unique teams and sort alphabetically
        const uniqueTeams = Array.from(new Set(
          response.players
            .map(player => player.team)
            .filter(Boolean) // Remove empty values
        )).sort()

        setTeams(uniqueTeams)
      } catch (error) {
        console.error('Error loading teams:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadTeams()
  }, [])

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  // Filter teams based on search term
  const filteredTeams = teams.filter(team =>
    team.toLowerCase().includes(searchTerm.toLowerCase())
  ).slice(0, 20)

  const toggleTeam = (team: string) => {
    const isSelected = value.includes(team)
    const newValue = isSelected ? value.filter(t => t !== team) : [...value, team]
    onValueChange(newValue)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const v = e.target.value
    setSearchTerm(v)
    setShowSuggestions(true)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      if (filteredTeams.length === 1) {
        e.preventDefault()
        toggleTeam(filteredTeams[0])
        setShowSuggestions(false)
      }
    } else if (e.key === 'Escape') {
      setShowSuggestions(false)
    }
  }

  const handleClearAll = () => {
    onValueChange([])
    setSearchTerm("")
    inputRef.current?.focus()
  }

  useEffect(() => {
    const root = areaRef.current
    if (!root) return
    const viewport = root.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement | null
    if (!viewport) return
    let to: any
    const onScroll = () => {
      setIsScrolling(true)
      clearTimeout(to)
      to = setTimeout(() => setIsScrolling(false), 300)
    }
    viewport.addEventListener('scroll', onScroll, { passive: true })
    return () => {
      viewport.removeEventListener('scroll', onScroll)
      clearTimeout(to)
    }
  }, [])

  return (
    <div className="relative">
      <div className="relative">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={isLoading ? "Loading teams..." : "Search teams..."}
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={() => setShowSuggestions(true)}
          onKeyDown={handleKeyDown}
          className="pl-8 pr-8"
          disabled={isLoading}
        />
        {(value.length > 0 || searchTerm) && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3 py-0"
            onClick={handleClearAll}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {showSuggestions && filteredTeams.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 mt-1 w-full rounded-md border bg-popover shadow-md"
        >
          <div ref={areaRef} className="h-auto max-h-60 overflow-y-auto pv-scroll-mini" data-scrolling={isScrolling ? 'true' : 'false'}>
            <div className="p-1 space-y-1">
              {filteredTeams.map(team => (
                <div
                  key={team}
                  role="option"
                  tabIndex={0}
                  className="w-full flex items-center gap-2 rounded-sm px-2 py-1.5 text-left hover:bg-accent cursor-pointer"
                  onClick={() => toggleTeam(team)}
                  onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); toggleTeam(team) } }}
                >
                  <Checkbox checked={value.includes(team)} onCheckedChange={() => toggleTeam(team)} />
                  <span className="text-sm">{team}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {value.length > 0 && (
        <div className="mt-2 flex flex-wrap gap-1">
          {value.map(team => (
            <Badge key={team} variant="secondary" className="text-xs">
              {team}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={() => toggleTeam(team)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}
