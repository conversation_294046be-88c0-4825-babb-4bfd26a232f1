"use client"

import { useState, useC<PERSON>back, useEffect, useMemo, useRef } from "react"
import { PlayerCard } from "@/components/player-card"
import { StatsFilter } from "@/components/stats-filter"
import { ComprehensiveStatsTable } from "@/components/comprehensive-stats-table"
import { PlayerBioTable } from "@/components/player-bio-table"
import { PlayerBasicStatsTable } from "@/components/player-basic-stats-table"
import { PlayerShootingStatsTable } from "@/components/player-shooting-stats-table"
import { PlayerAdvancedStatsTable } from "@/components/player-advanced-stats-table"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { getPlayerData } from "@/utils/data-transform"
import { TableIcon, UsersIcon, Search, Filter, X, ChevronDown, KeyRound } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Player } from "@/types/player"
import { PlayerStatsModal } from "@/components/player-stats-modal"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuRadioGroup, DropdownMenuRadioItem } from "@/components/ui/dropdown-menu"
import LoadingLogo from "@/components/loading-logo"
import { useIndex } from "@/components/index-provider"
import { StatsKey } from "@/components/stats-key"

export interface FilterCriteria {
  positions: string[]
  teams: string[]
  conferences: string[]
  classes: string[]
  minPlayerIndex: number
  maxPlayerIndex: number
  minHeight: number
  maxHeight: number
  minWeight: number
  maxWeight: number
  minGP: number
  maxGP: number
  minMPG: number
  maxMPG: number
  minPPG: number
  maxPPG: number
  minFGM: number
  maxFGM: number
  minFGA: number
  maxFGA: number
  minFGP: number
  maxFGP: number
  min3PM: number
  max3PM: number
  min3PA: number
  max3PA: number
  min3PP: number
  max3PP: number
  minFTM: number
  maxFTM: number
  minFTA: number
  maxFTA: number
  minFTP: number
  maxFTP: number
  minORB: number
  maxORB: number
  minDRB: number
  maxDRB: number
  minRPG: number
  maxRPG: number
  minAPG: number
  maxAPG: number
  minSPG: number
  maxSPG: number
  minBPG: number
  maxBPG: number
  minTOV: number
  maxTOV: number
  minPF: number
  maxPF: number
  // Offensive Advanced Stats
  minTS?: number
  maxTS?: number
  minEFG?: number
  maxEFG?: number
  minTotalS?: number
  maxTotalS?: number
  minORBPct?: number
  maxORBPct?: number
  minASTPct?: number
  maxASTPct?: number
  minTOVPct?: number
  maxTOVPct?: number
  minUSG?: number
  maxUSG?: number
  minPPR?: number
  maxPPR?: number
  minPPS?: number
  maxPPS?: number
  minORtg?: number
  maxORtg?: number
  minFTRate?: number
  maxFTRate?: number
  minFIC?: number
  maxFIC?: number
  minPER?: number
  maxPER?: number
  // Defensive Advanced Stats
  minDRBPct?: number
  maxDRBPct?: number
  minTRBPct?: number
  maxTRBPct?: number
  minSTLPct?: number
  maxSTLPct?: number
  minBLKPct?: number
  maxBLKPct?: number
  minDRtg?: number
  maxDRtg?: number
  minEDiff?: number
  maxEDiff?: number
  // PlayVision Indexes
  minPOI?: number
  maxPOI?: number
  minPDI?: number
  maxPDI?: number
  minPEI?: number
  maxPEI?: number
  minPUI?: number
  maxPUI?: number
  active: boolean
  searchTerm: string
  transferPortal?: boolean
  statusEntered?: boolean
  statusCommitted?: boolean
  dateFrom?: string
  dateTo?: string
  notRated?: boolean
  [key: string]: number | string | boolean | string[] | undefined
}

type ViewType = "cards" | "table" | "bio" | "basic" | "shooting" | "advanced" | "key"

export default function StatsFilterPage() {
  const headerRef = useRef<HTMLDivElement>(null)
  const [overlayTop, setOverlayTop] = useState<number>(0)
  const triggerRef = useRef<HTMLButtonElement>(null)
  const [overlayLeft, setOverlayLeft] = useState<number>(0)
  const [season, setSeason] = useState<string | undefined>(undefined)
  const [seasons, setSeasons] = useState<string[]>([])
  const [isSeasonsLoading, setIsSeasonsLoading] = useState<boolean>(false)
  const [seasonMenuOpen, setSeasonMenuOpen] = useState<boolean>(false)
  const seasonTriggerRef = useRef<HTMLButtonElement>(null)
  const [seasonMenuWidth, setSeasonMenuWidth] = useState<number>(0)
  const [players, setPlayers] = useState<any[]>([])
  const [filteredPlayers, setFilteredPlayers] = useState<any[]>([])
  const [activeView, setActiveView] = useState<ViewType>("cards")
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null)
  const [showStatsModal, setShowStatsModal] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(12)
  const [filtersOpen, setFiltersOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterRanges, setFilterRanges] = useState<Partial<FilterCriteria>>({
    minGP: 0,
    maxGP: 82,
    minPlayerIndex: 0,
    maxPlayerIndex: 100
  })
  const scrollRef = useRef<HTMLDivElement>(null)
  const [isScrolling, setIsScrolling] = useState(false)
  const { useCustomIndex, indexName, setUseCustomIndex, savedIndexes, applySavedIndex, applyIndexToPlayers, useSeasonIndex, setUseSeasonIndex } = useIndex()
  
  useEffect(() => {
    const node = scrollRef.current
    if (!node) return
    let timeout: any
    const onScroll = () => {
      setIsScrolling(true)
      clearTimeout(timeout)
      timeout = setTimeout(() => setIsScrolling(false), 400)
    }
    node.addEventListener('scroll', onScroll, { passive: true })
    return () => {
      node.removeEventListener('scroll', onScroll as any)
      clearTimeout(timeout)
    }
  }, [scrollRef])

  // Reset page when view changes
  useEffect(() => {
    setCurrentPage(1)
  }, [activeView])

  // Load seasons on mount
  useEffect(() => {
    const loadSeasons = async () => {
      try {
        setIsSeasonsLoading(true)
        const res = await fetch('/api/seasons', { cache: 'no-store' })
        const json = await res.json()
        const list: string[] = Array.isArray(json?.seasons) ? json.seasons : []
        setSeasons(list)
        if (!season && list.length > 0) {
          setSeason(list[0])
        }
      } catch (e) {
        console.error('Failed to load seasons', e)
      } finally {
        setIsSeasonsLoading(false)
      }
    }
    loadSeasons()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Track trigger width so the menu matches button width
  useEffect(() => {
    const updateWidth = () => {
      if (seasonTriggerRef.current) {
        setSeasonMenuWidth(seasonTriggerRef.current.offsetWidth)
      }
    }
    updateWidth()
    window.addEventListener('resize', updateWidth)
    return () => window.removeEventListener('resize', updateWidth)
  }, [])

  // Track header bottom to position overlay directly underneath
  useEffect(() => {
    const recompute = () => {
      if (triggerRef.current) {
        const rect = triggerRef.current.getBoundingClientRect()
        setOverlayTop(rect.bottom) // viewport coordinates for fixed positioning
        setOverlayLeft(rect.left)
      } else if (headerRef.current) {
        const rect = headerRef.current.getBoundingClientRect()
        setOverlayTop(rect.bottom)
        setOverlayLeft(rect.left)
      }
    }
    recompute()
    window.addEventListener("resize", recompute)
    window.addEventListener("scroll", recompute, { passive: true })
    return () => {
      window.removeEventListener("resize", recompute)
      window.removeEventListener("scroll", recompute)
    }
  }, [])

  // Recompute when the overlay is opened to ensure correct anchor
  useEffect(() => {
    if (filtersOpen) {
      requestAnimationFrame(() => {
        if (triggerRef.current) {
          const rect = triggerRef.current.getBoundingClientRect()
          setOverlayTop(rect.bottom)
          setOverlayLeft(rect.left)
        }
      })
    }
  }, [filtersOpen])

  // Lock body scroll when overlay is open
  useEffect(() => {
    if (filtersOpen) {
      const prev = document.body.style.overflow
      document.body.style.overflow = "hidden"
      return () => { document.body.style.overflow = prev }
    }
  }, [filtersOpen])

  // Move hooks before any conditional returns
  const heightToInches = (heightStr: string): number => {
    if (!heightStr) return 0

    // Handle 5-11 format
    if (heightStr.includes('-')) {
      const parts = heightStr.split('-')
      if (parts.length === 2) {
        const feet = Number.parseInt(parts[0], 10)
        const inches = Number.parseInt(parts[1], 10)

        if (!isNaN(feet) && !isNaN(inches) && inches < 12) {
          return feet * 12 + inches
        }
      }
    }

    // Handle 511 format (first digit is feet, rest are inches)
    const cleanStr = heightStr.replace(/\D/g, '')
    if (/^\d+$/.test(cleanStr)) {
      if (cleanStr.length === 3) {
        const feet = Number.parseInt(cleanStr[0], 10)
        const inches = Number.parseInt(cleanStr.substring(1), 10)

        if (!isNaN(feet) && !isNaN(inches) && inches < 12) {
          return feet * 12 + inches
        }
      }
    }

    return 0
  }

  const handleFilterChange = useCallback((filterCriteria: FilterCriteria) => {
    const filtered = players.filter((player) => {
      // Position filter (OR)
      if (filterCriteria.positions && filterCriteria.positions.length > 0) {
        if (!filterCriteria.positions.includes(player.position)) {
          return false
        }
      }

      // Team filter (OR)
      if (filterCriteria.teams && filterCriteria.teams.length > 0) {
        if (!player.team || !filterCriteria.teams.includes(player.team)) {
          return false
        }
      }

      // Conference filter (OR)
      if (filterCriteria.conferences && filterCriteria.conferences.length > 0) {
        if (!player.conference || !filterCriteria.conferences.includes(player.conference)) {
          return false
        }
      }

      // Class filter - multi-select
      if (filterCriteria.classes && filterCriteria.classes.length > 0) {
        if (!player.class || !filterCriteria.classes.includes(player.class)) {
          return false
        }
      }

      // Player Index filter
      if (player.playerIndex < filterCriteria.minPlayerIndex || player.playerIndex > filterCriteria.maxPlayerIndex) {
        return false
      }

      // Physical attributes
      const heightInches = heightToInches(player.height)
      if (heightInches < filterCriteria.minHeight || heightInches > filterCriteria.maxHeight) {
        return false
      }

      if (player.weight < filterCriteria.minWeight || player.weight > filterCriteria.maxWeight) {
        return false
      }

      // Basic stats ranges
      if (player.stats?.gp !== undefined && (player.stats.gp < filterCriteria.minGP || player.stats.gp > filterCriteria.maxGP)) {
        return false
      }

      if (player.stats?.mpg !== undefined && (player.stats.mpg < filterCriteria.minMPG || player.stats.mpg > filterCriteria.maxMPG)) {
        return false
      }

      if ((player.stats?.ppg || 0) < filterCriteria.minPPG || (player.stats?.ppg || 0) > filterCriteria.maxPPG) {
        return false
      }

      if (player.stats?.fgm !== undefined && (player.stats.fgm < filterCriteria.minFGM || player.stats.fgm > filterCriteria.maxFGM)) {
        return false
      }

      if (player.stats?.fga !== undefined && (player.stats.fga < filterCriteria.minFGA || player.stats.fga > filterCriteria.maxFGA)) {
        return false
      }

      if ((player.stats.fg || 0) < filterCriteria.minFGP || (player.stats.fg || 0) > filterCriteria.maxFGP) {
        return false
      }

      if (player.stats?.tpm !== undefined && (player.stats.tpm < filterCriteria.min3PM || player.stats.tpm > filterCriteria.max3PM)) {
        return false
      }

      if (player.stats?.tpa !== undefined && (player.stats.tpa < filterCriteria.min3PA || player.stats.tpa > filterCriteria.max3PA)) {
        return false
      }

      if (player.stats?.tpp !== undefined && (player.stats.tpp < filterCriteria.min3PP || player.stats.tpp > filterCriteria.max3PP)) {
        return false
      }

      if (player.stats?.ftm !== undefined && (player.stats.ftm < filterCriteria.minFTM || player.stats.ftm > filterCriteria.maxFTM)) {
        return false
      }

      if (player.stats?.fta !== undefined && (player.stats.fta < filterCriteria.minFTA || player.stats.fta > filterCriteria.maxFTA)) {
        return false
      }

      if (player.stats?.ftp !== undefined && (player.stats.ftp < filterCriteria.minFTP || player.stats.ftp > filterCriteria.maxFTP)) {
        return false
      }

      if (player.stats?.orb !== undefined && (player.stats.orb < filterCriteria.minORB || player.stats.orb > filterCriteria.maxORB)) {
        return false
      }

      if ((player.advancedStats?.drb || 0) < filterCriteria.minDRB || (player.advancedStats?.drb || 0) > filterCriteria.maxDRB) {
        return false
      }

      if ((player.stats.rpg || 0) < filterCriteria.minRPG || (player.stats.rpg || 0) > filterCriteria.maxRPG) {
        return false
      }

      if ((player.stats.apg || 0) < filterCriteria.minAPG || (player.stats.apg || 0) > filterCriteria.maxAPG) {
        return false
      }

      if ((player.stats.spg || 0) < filterCriteria.minSPG || (player.stats.spg || 0) > filterCriteria.maxSPG) {
        return false
      }

      if ((player.stats.bpg || 0) < filterCriteria.minBPG || (player.stats.bpg || 0) > filterCriteria.maxBPG) {
        return false
      }

      if ((player.stats.tov || 0) < filterCriteria.minTOV || (player.stats.tov || 0) > filterCriteria.maxTOV) {
        return false
      }

      if ((player.stats.pf || 0) < filterCriteria.minPF || (player.stats.pf || 0) > filterCriteria.maxPF) {
        return false
      }

      // Offensive Advanced Stats
      if ((player.advancedStats?.ts_pct || 0) < (filterCriteria.minTS ?? 0) || (player.advancedStats?.ts_pct || 0) > (filterCriteria.maxTS ?? 999)) {
        return false
      }

      if ((player.advancedStats?.efg || 0) < (filterCriteria.minEFG ?? 0) || (player.advancedStats?.efg || 0) > (filterCriteria.maxEFG ?? 999)) {
        return false
      }

      if ((player.advancedStats?.totalS || 0) < (filterCriteria.minTotalS ?? 0) || (player.advancedStats?.totalS || 0) > (filterCriteria.maxTotalS ?? 999)) {
        return false
      }

      if ((player.advancedStats?.orb || 0) < (filterCriteria.minORBPct ?? 0) || (player.advancedStats?.orb || 0) > (filterCriteria.maxORBPct ?? 999)) {
        return false
      }

      if ((player.advancedStats?.ast || 0) < (filterCriteria.minASTPct ?? 0) || (player.advancedStats?.ast || 0) > (filterCriteria.maxASTPct ?? 999)) {
        return false
      }

      if ((player.advancedStats?.tov || 0) < (filterCriteria.minTOVPct ?? 0) || (player.advancedStats?.tov || 0) > (filterCriteria.maxTOVPct ?? 999)) {
        return false
      }

      if ((player.advancedStats?.usg || 0) < (filterCriteria.minUSG ?? 0) || (player.advancedStats?.usg || 0) > (filterCriteria.maxUSG ?? 999)) {
        return false
      }

      if ((player.advancedStats?.ppr || 0) < (filterCriteria.minPPR ?? -999) || (player.advancedStats?.ppr || 0) > (filterCriteria.maxPPR ?? 999)) {
        return false
      }

      if ((player.advancedStats?.pps || 0) < (filterCriteria.minPPS ?? 0) || (player.advancedStats?.pps || 0) > (filterCriteria.maxPPS ?? 999)) {
        return false
      }

      if ((player.advancedStats?.ortg || 0) < (filterCriteria.minORtg ?? -9999) || (player.advancedStats?.ortg || 0) > (filterCriteria.maxORtg ?? 9999)) {
        return false
      }

      if ((player.advancedStats?.ftrate || 0) < (filterCriteria.minFTRate ?? 0) || (player.advancedStats?.ftrate || 0) > (filterCriteria.maxFTRate ?? 999)) {
        return false
      }

      if ((player.advancedStats?.fic || 0) < (filterCriteria.minFIC ?? -9999) || (player.advancedStats?.fic || 0) > (filterCriteria.maxFIC ?? 9999)) {
        return false
      }

      if ((player.advancedStats?.per || 0) < (filterCriteria.minPER ?? -9999) || (player.advancedStats?.per || 0) > (filterCriteria.maxPER ?? 9999)) {
        return false
      }

      // Defensive Advanced Stats
      if ((player.advancedStats?.drb || 0) < (filterCriteria.minDRBPct ?? 0) || (player.advancedStats?.drb || 0) > (filterCriteria.maxDRBPct ?? 999)) {
        return false
      }

      if ((player.advancedStats?.trb || 0) < (filterCriteria.minTRBPct ?? 0) || (player.advancedStats?.trb || 0) > (filterCriteria.maxTRBPct ?? 999)) {
        return false
      }

      if ((player.advancedStats?.stl || 0) < (filterCriteria.minSTLPct ?? 0) || (player.advancedStats?.stl || 0) > (filterCriteria.maxSTLPct ?? 999)) {
        return false
      }

      if ((player.advancedStats?.blk || 0) < (filterCriteria.minBLKPct ?? 0) || (player.advancedStats?.blk || 0) > (filterCriteria.maxBLKPct ?? 999)) {
        return false
      }

      if ((player.advancedStats?.drtg || 0) < (filterCriteria.minDRtg ?? -9999) || (player.advancedStats?.drtg || 0) > (filterCriteria.maxDRtg ?? 9999)) {
        return false
      }

      if ((player.advancedStats?.ediff || 0) < (filterCriteria.minEDiff ?? -9999) || (player.advancedStats?.ediff || 0) > (filterCriteria.maxEDiff ?? 9999)) {
        return false
      }

      // PlayVision Indexes
      if ((player.advancedStats?.poi || 0) < (filterCriteria.minPOI ?? -9999) || (player.advancedStats?.poi || 0) > (filterCriteria.maxPOI ?? 9999)) {
        return false
      }

      if ((player.advancedStats?.pdi || 0) < (filterCriteria.minPDI ?? -9999) || (player.advancedStats?.pdi || 0) > (filterCriteria.maxPDI ?? 9999)) {
        return false
      }

      if ((player.advancedStats?.pei || 0) < (filterCriteria.minPEI ?? -9999) || (player.advancedStats?.pei || 0) > (filterCriteria.maxPEI ?? 9999)) {
        return false
      }

      if ((player.advancedStats?.pui || 0) < (filterCriteria.minPUI ?? -9999) || (player.advancedStats?.pui || 0) > (filterCriteria.maxPUI ?? 9999)) {
        return false
      }

      // Status filters
      if (filterCriteria.statusEntered && !player.status?.includes("Entered")) {
        return false
      }

      if (filterCriteria.statusCommitted && !player.status?.includes("Committed")) {
        return false
      }

      if (!filterCriteria.notRated && player.playerIndex === 0) {
        return false
      }

      if (filterCriteria.active && player.active === false) {
        return false
      }

      return true
    })

    setFilteredPlayers(filtered)
  }, [players])

  // Load players whenever season changes
  useEffect(() => {
    const loadPlayers = async () => {
      try {
        setIsLoading(true)
        const response = await getPlayerData(season ? { season } : {})
        const applied = applyIndexToPlayers(response.players)
        setPlayers(applied)
        setFilteredPlayers(applied)
      } catch (error) {
        console.error('Error loading players:', error)
      } finally {
        setIsLoading(false)
      }
    }
    loadPlayers()
  }, [season, applyIndexToPlayers])
  
  // Re-apply whenever index weights/toggle change
  useEffect(() => {
    setPlayers((prev) => applyIndexToPlayers(prev))
    setFilteredPlayers((prev) => applyIndexToPlayers(prev))
  }, [applyIndexToPlayers])

  // Helper function to get players after search and filters
  const getFilteredAndSearchedPlayers = useCallback(() => {
    // First apply search to the full players array
    const searchFiltered = searchTerm
      ? players.filter(player =>
          (player.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
          (player.player_name?.toLowerCase() || '').includes(searchTerm.toLowerCase()))
      : players

    // Then apply other filters to the search results
    return searchFiltered.filter((player) => {
      // Position filter (OR)
      if (filterRanges.positions && filterRanges.positions.length > 0) {
        if (!filterRanges.positions.includes(player.position)) {
          return false
        }
      }

      // Team filter (OR)
      if (filterRanges.teams && filterRanges.teams.length > 0) {
        if (!player.team || !filterRanges.teams.includes(player.team)) {
          return false
        }
      }

      // Conference filter (OR)
      if (filterRanges.conferences && filterRanges.conferences.length > 0) {
        if (!player.conference || !filterRanges.conferences.includes(player.conference)) {
          return false
        }
      }

      // Class filter - multi-select
      if (filterRanges.classes && filterRanges.classes.length > 0) {
        if (!player.class || !filterRanges.classes.includes(player.class)) {
          return false
        }
      }

      // Player Index filter
      if ((player.playerIndex ?? 0) < (filterRanges.minPlayerIndex ?? 0) || (player.playerIndex ?? 0) > (filterRanges.maxPlayerIndex ?? 100)) {
        return false
      }

      // Physical attributes
      const heightInches = heightToInches(player.height)
      if (heightInches < (filterRanges.minHeight ?? 0) || heightInches > (filterRanges.maxHeight ?? 88)) {
        return false
      }

      if ((player.weight ?? 0) < (filterRanges.minWeight ?? 0) || (player.weight ?? 0) > (filterRanges.maxWeight ?? 300)) {
        return false
      }

      // Basic stats ranges
      if (player.stats?.gp !== undefined && (player.stats.gp < (filterRanges.minGP ?? 0) || player.stats.gp > (filterRanges.maxGP ?? 82))) {
        return false
      }

      if (player.stats?.mpg !== undefined && (player.stats.mpg < (filterRanges.minMPG ?? 0) || player.stats.mpg > (filterRanges.maxMPG ?? 40))) {
        return false
      }

      if ((player.stats?.ppg || 0) < (filterRanges.minPPG ?? 0) || (player.stats?.ppg || 0) > (filterRanges.maxPPG ?? 50)) {
        return false
      }

      return true
    })
  }, [players, searchTerm, filterRanges])

  const paginatedPlayers = useMemo(() => {
    const filterAndSearchFiltered = getFilteredAndSearchedPlayers()

    const sorted = activeView === "cards"
      ? [...filterAndSearchFiltered].sort((a, b) => (b.playerIndex ?? 0) - (a.playerIndex ?? 0))
      : filterAndSearchFiltered

    // Then paginate
    const startIndex = (currentPage - 1) * itemsPerPage
    return sorted.slice(startIndex, startIndex + itemsPerPage)
  }, [getFilteredAndSearchedPlayers, currentPage, itemsPerPage, activeView])

  const totalPages = Math.ceil(getFilteredAndSearchedPlayers().length / itemsPerPage)

  if (isLoading) {
    const SeasonText = season ? season.replace('_', '–') : undefined
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingLogo size={128} label={`Loading${SeasonText ? ` ${SeasonText}` : ''}…`} />
      </div>
    )
  }

  const handlePlayerSelect = (player: any) => {
    setSelectedPlayer(player as Player)
    setShowStatsModal(true)
  }

  return (
    <div className="min-h-screen bg-background">

      <main className="container px-4 sm:px-6 lg:px-8 py-6 mx-auto max-w-full">
        {/* Header row with count, tabs, and Filters button */}
        <div ref={headerRef} className="sticky top-16 z-30 bg-background border-b pb-4 pt-2 -mx-4 px-4 sm:-mx-6 sm:px-6 lg:-mx-8 lg:px-8">
          <div className="flex items-end justify-between" style={{ gap: '16px' }}>
            {/* Left side: Filters, Season, Index, Search */}
            <div className="flex items-end" style={{ gap: '16px' }}>
              <Collapsible open={filtersOpen} onOpenChange={setFiltersOpen}>
                <CollapsibleTrigger asChild>
                  <Button ref={triggerRef} type="button" variant="outline" className="shrink-0">
                    <Filter className="h-4 w-4 mr-2" /> Filters
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent asChild>
                  <div
                    className="fixed bottom-0 z-50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=open]:fade-in-0 data-[state=closed]:fade-out-0"
                    style={{ top: (overlayTop || 0) + 8, left: overlayLeft || 0, right: overlayLeft || 0 }}
                  >
                    <div className="absolute inset-0 bg-black/60" onClick={() => setFiltersOpen(false)} />
                    <div className="relative z-10 h-full w-full p-0">
                      <div className="w-full h-full">
                        <div ref={scrollRef} className="w-full h-full overflow-y-auto bg-background shadow-xl animate-in slide-in-from-top-2 duration-200 pv-scroll relative" data-scrolling={isScrolling ? 'true' : 'false'} style={{ scrollbarGutter: 'stable both-edges' }}>
                          <StatsFilter
                            onFilterChange={handleFilterChange}
                            filterRanges={filterRanges}
                            onClose={() => setFiltersOpen(false)}
                            onReset={() => setFilteredPlayers(players)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
              <DropdownMenu open={seasonMenuOpen} onOpenChange={setSeasonMenuOpen}>
                <DropdownMenuTrigger asChild>
                  <Button ref={seasonTriggerRef} type="button" variant="outline" className="shrink-0 inline-flex items-center gap-2" aria-expanded={seasonMenuOpen}>
                    <span>{isSeasonsLoading ? 'Season…' : season ? season.replace('_', '–') : 'Season'}</span>
                    <ChevronDown className={`h-4 w-4 transition-transform ${seasonMenuOpen ? 'rotate-180' : 'rotate-0'}`} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" side="bottom" sideOffset={6} className="p-1" style={{ width: seasonMenuWidth || undefined }}>
                  {seasons.length > 0 ? (
                    <div className="py-0.5">
                      {seasons.map((s) => (
                        <DropdownMenuItem
                          key={s}
                          onClick={() => setSeason(s)}
                          className={`rounded-sm hover:bg-accent/60 ${season === s ? 'bg-accent/40' : ''}`}
                        >
                          {s.replace('_', '–')}
                        </DropdownMenuItem>
                      ))}
                    </div>
                  ) : (
                    <div className="px-2 py-1.5 text-sm text-muted-foreground">{isSeasonsLoading ? 'Loading…' : 'No seasons'}</div>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <span>{useCustomIndex ? `Custom: ${indexName}` : useSeasonIndex ? "Season" : "All-Time"}</span>
                    <ChevronDown className="h-4 w-4 opacity-70" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  <DropdownMenuLabel>Select Index</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup value={useCustomIndex ? indexName : useSeasonIndex ? "__season__" : "__alltime__"} onValueChange={(val) => {
                    if (val === "__alltime__") {
                      setUseCustomIndex(false)
                      setUseSeasonIndex(false)
                    } else if (val === "__season__") {
                      setUseCustomIndex(false)
                      setUseSeasonIndex(true)
                    } else {
                      applySavedIndex(val)
                    }
                  }}>
                    <DropdownMenuRadioItem value="__alltime__">All-Time</DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="__season__">Season</DropdownMenuRadioItem>
                    <DropdownMenuSeparator />
                    {savedIndexes.map((idx) => (
                      <DropdownMenuRadioItem key={idx.name} value={idx.name}>{idx.name}</DropdownMenuRadioItem>
                    ))}
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>
              
              {/* Search bar positioned to the right of index selector */}
              <div className="relative flex-shrink-0">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search players..."
                  className="pl-8 w-48 focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value)
                    setCurrentPage(1)
                  }}
                />
              </div>
            </div>
            
            {/* Right side: Player count above table toggler, both right-aligned */}
            <div className="flex flex-col items-end gap-2">
              {/* Player count above the toggler */}
              <div className="text-xs text-muted-foreground">
                {getFilteredAndSearchedPlayers().length} Players Found
              </div>
              
              {/* Table toggler */}
              <Tabs
                defaultValue="cards"
                onValueChange={(value) => setActiveView(value as ViewType)}
                className="flex-shrink-0"
              >
                <TabsList className="grid w-auto grid-cols-7">
                  <TabsTrigger value="cards" className="flex items-center gap-2 px-2">
                    <UsersIcon className="w-4 h-4" />
                    <span className="text-xs">Cards</span>
                  </TabsTrigger>
                  <TabsTrigger value="table" className="flex items-center gap-2 px-2">
                    <TableIcon className="w-4 h-4" />
                    <span className="text-xs">All Stats</span>
                  </TabsTrigger>
                  <TabsTrigger value="bio" className="flex items-center justify-center px-2">
                    <span className="text-xs font-medium">Bio</span>
                  </TabsTrigger>
                  <TabsTrigger value="basic" className="flex items-center justify-center px-2">
                    <span className="text-xs font-medium">Basic</span>
                  </TabsTrigger>
                  <TabsTrigger value="shooting" className="flex items-center justify-center px-2">
                    <span className="text-xs font-medium">Shooting</span>
                  </TabsTrigger>
                  <TabsTrigger value="advanced" className="flex items-center justify-center px-2">
                    <span className="text-xs font-medium">Advanced</span>
                  </TabsTrigger>
                  <TabsTrigger value="key" className="flex items-center gap-2 px-2">
                    <KeyRound className="w-4 h-4" />
                    <span className="text-xs">Key</span>
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>
        </div>



        {/* Content area */}
        <div className="space-y-6 pt-4">
          {activeView === "cards" && (
            <>
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {paginatedPlayers.map((player) => (
                  <PlayerCard
                    key={player.player_id || player.id}
                    player={player}
                    onSelect={() => handlePlayerSelect(player)}
                    isSelected={selectedPlayer?.player_id === player.player_id || selectedPlayer?.id === player.id}
                  />
                ))}
              </div>
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    {searchTerm && (
                      <span className="mr-2">
                        Search: "{searchTerm}" •
                      </span>
                    )}
                    Showing {(currentPage - 1) * itemsPerPage + 1}-
                    {Math.min(currentPage * itemsPerPage, getFilteredAndSearchedPlayers().length)} of {getFilteredAndSearchedPlayers().length}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <div className="text-sm">
                      Page {currentPage} of {totalPages}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}

          {activeView === "table" && (
            <ComprehensiveStatsTable
              players={getFilteredAndSearchedPlayers()}
              selectedPlayerId={selectedPlayer?.player_id ?? selectedPlayer?.id}
              onPlayerSelect={handlePlayerSelect}
            />
          )}

          {activeView === "bio" && (
            <PlayerBioTable
              players={getFilteredAndSearchedPlayers()}
              selectedPlayerId={selectedPlayer?.player_id ?? (selectedPlayer?.id as number | undefined)}
              onPlayerSelect={handlePlayerSelect}
            />
          )}

          {activeView === "basic" && (
            <PlayerBasicStatsTable
              players={getFilteredAndSearchedPlayers()}
              selectedPlayerId={selectedPlayer?.player_id ?? (selectedPlayer?.id as number | undefined)}
              onPlayerSelect={handlePlayerSelect}
            />
          )}

          {activeView === "shooting" && (
            <PlayerShootingStatsTable
              players={getFilteredAndSearchedPlayers()}
              selectedPlayerId={selectedPlayer?.player_id ?? (selectedPlayer?.id as number | undefined)}
              onPlayerSelect={handlePlayerSelect}
            />
          )}

          {activeView === "advanced" && (
            <PlayerAdvancedStatsTable
              players={getFilteredAndSearchedPlayers()}
              selectedPlayerId={selectedPlayer?.player_id ?? (selectedPlayer?.id as number | undefined)}
              onPlayerSelect={handlePlayerSelect}
            />
          )}
          {activeView === "key" && (
            <StatsKey />
          )}
        </div>
      </main>

      {/* Player Stats Modal */}
      <PlayerStatsModal
        player={selectedPlayer}
        isOpen={showStatsModal}
        onClose={() => setShowStatsModal(false)}
      />
    </div>
  )
}

