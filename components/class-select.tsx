"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { X, ChevronDown } from "lucide-react"
import { getPlayerData } from "@/utils/data-transform"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Checkbox } from "@/components/ui/checkbox"

interface ClassSelectProps {
  value: string[]
  onValueChange: (value: string[]) => void
}

export function ClassSelect({ value, onValueChange }: ClassSelectProps) {
  const [classes, setClasses] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Load classes on component mount
  useEffect(() => {
    const loadClasses = async () => {
      try {
        setIsLoading(true)
        const response = await getPlayerData()

        // Extract unique classes
        const uniqueClasses = Array.from(new Set(
          response.players
            .map(player => (player as any).class)
            .filter(Boolean) // Remove empty values
        ))

        // Define custom order
        const classOrder = ['Fr', 'So', 'Jr', 'Sr', 'RS-Fr', 'RS-So', 'RS-Jr', 'RS-Sr'] // Add your desired order
        
        // Sort by custom order, then alphabetically for any not in the order
        const sortedClasses = uniqueClasses.sort((a, b) => {
          const aIndex = classOrder.indexOf(a)
          const bIndex = classOrder.indexOf(b)
          
          if (aIndex !== -1 && bIndex !== -1) {
            return aIndex - bIndex // Both in custom order
          } else if (aIndex !== -1) {
            return -1 // a is in custom order, comes first
          } else if (bIndex !== -1) {
            return 1 // b is in custom order, comes first
          } else {
            return a.localeCompare(b) // Neither in custom order, sort alphabetically
          }
        })

        setClasses(sortedClasses)
      } catch (error) {
        console.error('Error loading classes:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadClasses()
  }, [])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleClassToggle = (className: string) => {
    const newValue = value.includes(className)
      ? value.filter(c => c !== className)
      : [...value, className]
    onValueChange(newValue)
  }

  const handleClearAll = () => {
    onValueChange([])
  }

  const handleSelectAll = () => {
    onValueChange([...classes])
  }

  return (
    <div className="space-y-2 relative" ref={dropdownRef}>
      <Button
        variant="outline"
        className="w-full justify-between text-left font-normal"
        disabled={isLoading}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="truncate">
          {isLoading 
            ? "Loading classes..." 
            : value.length === 0 
              ? "Select classes..." 
              : `${value.length} class${value.length !== 1 ? 'es' : ''} selected`
          }
        </span>
        <ChevronDown className="h-4 w-4 opacity-50" />
      </Button>

      {isOpen && (
        <div className="absolute z-50 mt-1 w-full rounded-md border bg-popover shadow-md">
          <div className="border-b p-2">
            <div className="flex justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSelectAll}
                disabled={classes.length === 0}
              >
                Select All
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearAll}
                disabled={value.length === 0}
              >
                Clear All
              </Button>
            </div>
          </div>
          <ScrollArea className="h-[200px]">
            <div className="p-2 space-y-2">
              {classes.map(className => (
                <div key={className} className="flex items-center space-x-2">
                  <Checkbox
                    id={`class-${className}`}
                    checked={value.includes(className)}
                    onCheckedChange={() => handleClassToggle(className)}
                  />
                  <label
                    htmlFor={`class-${className}`}
                    className="text-sm font-normal cursor-pointer flex-1"
                  >
                    {className}
                  </label>
                </div>
              ))}
              {classes.length === 0 && !isLoading && (
                <div className="text-sm text-muted-foreground p-2">
                  No classes found
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      )}

      {/* Selected classes badges */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {value.map(className => (
            <Badge
              key={className}
              variant="secondary"
              className="text-xs"
            >
              {className}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={() => handleClassToggle(className)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
} 