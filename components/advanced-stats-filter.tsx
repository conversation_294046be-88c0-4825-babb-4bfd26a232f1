"use client"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"

interface AdvancedStatsFilterProps {
  filters: any
  setFilters: (filters: any) => void
}

export function AdvancedStatsFilter({ filters, setFilters }: AdvancedStatsFilterProps) {
  return (
    <Tabs defaultValue="offensive" className="w-full">
      <TabsList className="grid grid-cols-2 w-full">
        <TabsTrigger value="offensive">Offensive</TabsTrigger>
        <TabsTrigger value="defensive">Defensive</TabsTrigger>
      </TabsList>

      <TabsContent value="offensive" className="space-y-4 mt-4">
        <Accordion type="multiple" defaultValue={[]}>
          <AccordionItem value="shooting">
            <AccordionTrigger>Shooting Efficiency</AccordionTrigger>
            <AccordionContent className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="space-y-2">
                  <Label>True Shooting % (TS%)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.minTS || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minTS: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.maxTS || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxTS: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Effective Field Goal % (eFG%)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.minEFG || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minEFG: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.maxEFG || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxEFG: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Total S%</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.minTotalS || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minTotalS: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.maxTotalS || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxTotalS: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="efficiency">
            <AccordionTrigger>Offensive Efficiency</AccordionTrigger>
            <AccordionContent className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="space-y-2">
                  <Label>Offensive Rating (ORtg)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={150}
                        value={filters.minORtg || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minORtg: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={150}
                        value={filters.maxORtg || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxORtg: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Points Per Shot (PPS)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={5}
                        step={0.1}
                        value={filters.minPPS || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minPPS: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={5}
                        step={0.1}
                        value={filters.maxPPS || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxPPS: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Free Throw Rate (FTRATE)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.minFTRate || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minFTRate: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.maxFTRate || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxFTRate: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="contribution">
            <AccordionTrigger>Offensive Contribution</AccordionTrigger>
            <AccordionContent className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="space-y-2">
                  <Label>Offensive Rebound % (ORB%)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.minORBPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minORBPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.maxORBPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxORBPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Assist % (AST%)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.minASTPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minASTPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.maxASTPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxASTPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Turnover % (TOV%)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.minTOVPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minTOVPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.maxTOVPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxTOVPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Usage Rate (USG%)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.minUSG || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minUSG: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.maxUSG || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxUSG: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Pure Point Rating (PPR)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={20}
                        step={0.1}
                        value={filters.minPPR || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minPPR: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={20}
                        step={0.1}
                        value={filters.maxPPR || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxPPR: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="advanced">
            <AccordionTrigger>Overall Player Metrics</AccordionTrigger>
            <AccordionContent className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="space-y-2">
                  <Label>Floor Impact Counter (FIC)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={50}
                        step={0.1}
                        value={filters.minFIC || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minFIC: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={50}
                        step={0.1}
                        value={filters.maxFIC || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxFIC: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Player Efficiency Rating (PER)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={40}
                        step={0.1}
                        value={filters.minPER || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minPER: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={40}
                        step={0.1}
                        value={filters.maxPER || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxPER: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>




        </Accordion>
      </TabsContent>

      <TabsContent value="defensive" className="space-y-4 mt-4">
        <Accordion type="multiple" defaultValue={[]}>
          <AccordionItem value="rebounding">
            <AccordionTrigger>Rebounding</AccordionTrigger>
            <AccordionContent className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="space-y-2">
                  <Label>Defensive Rebound % (DRB%)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.minDRBPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minDRBPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.maxDRBPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxDRBPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Total Rebound % (TRB%)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.minTRBPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minTRBPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.maxTRBPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxTRBPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="defense">
            <AccordionTrigger>Defensive Impact</AccordionTrigger>
            <AccordionContent className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="space-y-2">
                  <Label>Steal % (STL%)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.minSTLPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minSTLPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.maxSTLPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxSTLPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Block % (BLK%)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.minBLKPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minBLKPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        value={filters.maxBLKPct || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxBLKPct: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="efficiency">
            <AccordionTrigger>Defensive Efficiency</AccordionTrigger>
            <AccordionContent className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="space-y-2">
                  <Label>Defensive Rating (DRtg)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={150}
                        value={filters.minDRtg || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minDRtg: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={150}
                        value={filters.maxDRtg || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxDRtg: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Efficiency Differential (eDiff)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={-50}
                        max={50}
                        step={0.1}
                        value={filters.minEDiff || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minEDiff: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={-50}
                        max={50}
                        step={0.1}
                        value={filters.maxEDiff || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxEDiff: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </TabsContent>
    </Tabs>
  )
}
