"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Bar, BarChart, Line, LineChart, Radar, RadarChart, PolarAngleAxis, PolarGrid } from "recharts"
import { Player } from "@/types/player";

// Define the valid stat keys based on the Player stats structure
type StatKeys = keyof Player['stats'];

export function StatsChart({ players }: { players: Player[] }) {
  const [statType, setStatType] = useState<StatKeys>("ppg")
  const [chartType, setChartType] = useState("bar")

  const statOptions = [
    { value: "ppg", label: "Points Per Game" },
    { value: "apg", label: "Assists Per Game" },
    { value: "rpg", label: "Rebounds Per Game" },
    { value: "spg", label: "Steals Per Game" },
    { value: "bpg", label: "Blocks Per Game" },
    { value: "fg", label: "Field Goal %" },
  ]

  // Prepare data for charts
  const chartData = players
    .slice(0, 10) // Limit to top 10 players for readability
    .map((player) => ({
      name: (player.name || 'Unknown').split(" ")[1] || 'Player', // Use last name for brevity
      [statType]: player.stats?.[statType] || 0,
      fullName: player.name || 'Unknown Player',
    }))
    .sort((a, b) => (Number(b[statType]) || 0) - (Number(a[statType]) || 0)) // Sort by selected stat

  // Prepare data for radar chart (top 5 players)
  const radarData = players.slice(0, 5).map((player) => ({
    name: (player.name || 'Unknown').split(" ")[1] || 'Player',
    ppg: player.stats?.ppg || 0,
    apg: player.stats?.apg || 0,
    rpg: player.stats?.rpg || 0,
    spg: player.stats?.spg || 0,
    bpg: player.stats?.bpg || 0,
    fg: player.stats?.fg_pct || 0,
    fullName: player.name || 'Unknown Player',
  }))

  // Generate colors for each player in radar chart
  const playerColors = [
    "hsl(var(--chart-1))",
    "hsl(var(--chart-2))",
    "hsl(var(--chart-3))",
    "hsl(var(--chart-4))",
    "hsl(var(--chart-5))",
  ]

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle>Player Statistics Comparison</CardTitle>
              <CardDescription>Visual comparison of player performance metrics</CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Select value={statType} onValueChange={(value) => setStatType(value as StatKeys)}>
                <SelectTrigger className="w-full sm:w-44">
                  <SelectValue placeholder="Select stat" />
                </SelectTrigger>
                <SelectContent>
                  {statOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Tabs value={chartType} onValueChange={setChartType} className="w-full sm:w-44">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="bar">Bar</TabsTrigger>
                  <TabsTrigger value="line">Line</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] w-full">
            <ChartContainer
              config={{
                [statType]: {
                  label: statOptions.find((opt) => opt.value === statType)?.label,
                  color: "hsl(var(--primary))",
                },
              }}
            >
              {chartType === "bar" ? (
                <BarChart data={chartData}>
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Bar dataKey={statType} radius={[4, 4, 0, 0]} className="fill-primary" />
                </BarChart>
              ) : (
                <LineChart data={chartData}>
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Line
                    type="monotone"
                    dataKey={statType}
                    strokeWidth={2}
                    activeDot={{ r: 6 }}
                    className="stroke-primary"
                  />
                </LineChart>
              )}
            </ChartContainer>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Player Skill Radar (Top 5 Players)</CardTitle>
          <CardDescription>Comprehensive view of player strengths across multiple categories</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[500px] w-full">
            <ChartContainer
              config={{
                ppg: { label: "Points", color: playerColors[0] },
                apg: { label: "Assists", color: playerColors[1] },
                rpg: { label: "Rebounds", color: playerColors[2] },
                spg: { label: "Steals", color: playerColors[3] },
                bpg: { label: "Blocks", color: playerColors[4] },
                fg: { label: "FG%", color: "hsl(var(--chart-5))" },
              }}
            >
              <RadarChart data={radarData} outerRadius={150}>
                <PolarGrid />
                <PolarAngleAxis dataKey="name" />
                <ChartTooltip content={<ChartTooltipContent />} />
                {radarData.map((entry, index) => (
                  <Radar
                    key={entry.name}
                    name={entry.fullName}
                    dataKey={statType}
                    stroke={playerColors[index % playerColors.length]}
                    fill={playerColors[index % playerColors.length]}
                    fillOpacity={0.2}
                  />
                ))}
              </RadarChart>
            </ChartContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

