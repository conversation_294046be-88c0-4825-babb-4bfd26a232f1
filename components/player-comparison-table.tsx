"use client"

import { useState } from "react"
import { Player } from "@/types/player"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { TableIcon, BarChart3Icon, ShieldIcon } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface PlayerComparisonTableProps {
  players: Player[]
}

export function PlayerComparisonTable({ players }: PlayerComparisonTableProps) {
  const [activeTab, setActiveTab] = useState("basic")

  // Function to determine the best value for a stat
  const getBestStatIndex = (statKey: string, isHigherBetter = true) => {
    if (players.length <= 1) return -1;

    let bestIndex = 0;
    let bestValue = isHigherBetter ? -Infinity : Infinity;

    players.forEach((player, index) => {
      // Handle both regular and advanced stats
      let statValue;
      if (statKey in player.stats) {
        statValue = player.stats[statKey as keyof Player['stats']];
      } else if (player.advancedStats && statKey in player.advancedStats) {
        statValue = player.advancedStats[statKey as keyof typeof player.advancedStats];
      }

      if (statValue === undefined || statValue === null) return;

      if (isHigherBetter) {
        if (statValue > bestValue) {
          bestValue = statValue;
          bestIndex = index;
        }
      } else {
        if (statValue < bestValue) {
          bestValue = statValue;
          bestIndex = index;
        }
      }
    });

    return bestIndex;
  };

  // Function to get color class for player index
  const getIndexColorClass = (index: number) => {
    if (index >= 90) return "text-green-500";
    if (index >= 80) return "text-emerald-500";
    if (index >= 70) return "text-blue-500";
    if (index >= 60) return "text-yellow-500";
    if (index >= 50) return "text-orange-500";
    return "text-red-500";
  };

  // Function to get progress color class for player index
  const getProgressColorClass = (index: number) => {
    if (index >= 90) return "bg-green-500";
    if (index >= 80) return "bg-emerald-500";
    if (index >= 70) return "bg-blue-500";
    if (index >= 60) return "bg-yellow-500";
    if (index >= 50) return "bg-orange-500";
    return "bg-red-500";
  };

  // Function to format percentage values
  const formatPercentage = (value: number) => {
    if (value === undefined || value === null) return "-";
    return `${value.toFixed(1)}%`;
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>Player Statistics Comparison</CardTitle>
        <p className="text-sm text-muted-foreground">Compare player stats side by side. The best stat in each category is highlighted in green.</p>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 w-full mb-4">
            <TabsTrigger value="basic">
              <TableIcon className="w-4 h-4 mr-2" />
              Basic Stats
            </TabsTrigger>
            <TabsTrigger value="offensive">
              <BarChart3Icon className="w-4 h-4 mr-2" />
              Offensive
            </TabsTrigger>
            <TabsTrigger value="defensive">
              <ShieldIcon className="w-4 h-4 mr-2" />
              Defensive
            </TabsTrigger>
          </TabsList>

          {/* Basic Stats Tab */}
          <TabsContent value="basic" className="space-y-4">
            {/* Player Info Section */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="font-semibold">Player Info</div>
              {players.map((player) => (
                <div key={player.id} className="space-y-2">
                  <div className="font-bold text-md">{player.name}</div>
                  <div className="flex items-center gap-2">
                    <Badge className="text-xs">{player.position}</Badge>
                    {player.transferPortal && (
                      <Badge
                        variant={player.status === 'Committed' ? "destructive" : "outline"}
                        className={`text-xs ${player.status === 'Committed' ? "" : "bg-green-600 hover:bg-green-700 text-white"}`}
                      >
                        {player.status}
                      </Badge>
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground">{player.team}</div>
                  <div className="text-sm text-muted-foreground">{player.class} · {player.height} · {player.weight} lbs</div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="text-sm">Player Index:</span>
                      <span className={`font-bold ${getIndexColorClass(player.playerIndex)}`}>
                        {player.playerIndex === 0 ? "NR" : player.playerIndex.toFixed(1)}
                      </span>
                    </div>
                    <Progress
                      value={player.playerIndex}
                      max={100}
                      className="h-2"
                      indicatorClassName={getProgressColorClass(player.playerIndex)}
                    />
                    <div className="grid grid-cols-4 gap-1 text-center mt-1 bg-muted/30 rounded-sm p-1">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <p className="text-xs font-medium">{player.advancedStats?.poi?.toFixed(1) || 'N/A'}</p>
                              <p className="text-[10px] text-muted-foreground">POI</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">POI is the PlayVision Offense Index</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <p className="text-xs font-medium">{player.advancedStats?.pdi?.toFixed(1) || 'N/A'}</p>
                              <p className="text-[10px] text-muted-foreground">PDI</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">PDI is the PlayVision Defense Index</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <p className="text-xs font-medium">{player.advancedStats?.pei?.toFixed(1) || 'N/A'}</p>
                              <p className="text-[10px] text-muted-foreground">PEI</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">PEI is the PlayVision Efficiency Index</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <p className="text-xs font-medium">{player.advancedStats?.pui?.toFixed(1) || 'N/A'}</p>
                              <p className="text-[10px] text-muted-foreground">PUI</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">PUI is the PlayVision Usage Index</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <hr className="my-4" />

            {/* Basic Stats Section */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2 font-semibold sticky left-0 bg-background">Player</th>
                    {players.every(p => p.stats.gp !== undefined) && (
                      <th className="p-2 text-center font-semibold">GP</th>
                    )}
                    {players.every(p => p.stats.mpg !== undefined) && (
                      <th className="p-2 text-center font-semibold">MPG</th>
                    )}
                    <th className="p-2 text-center font-semibold">PPG</th>
                    {players.every(p => p.stats.fgm !== undefined) && (
                      <th className="p-2 text-center font-semibold">FGM</th>
                    )}
                    {players.every(p => p.stats.fga !== undefined) && (
                      <th className="p-2 text-center font-semibold">FGA</th>
                    )}
                    {players.every(p => p.stats.fg !== undefined) && (
                      <th className="p-2 text-center font-semibold">FG%</th>
                    )}
                    {players.every(p => p.stats.tpm !== undefined) && (
                      <th className="p-2 text-center font-semibold">3PM</th>
                    )}
                    {players.every(p => p.stats.tpa !== undefined) && (
                      <th className="p-2 text-center font-semibold">3PA</th>
                    )}
                    {players.every(p => p.stats.tpp !== undefined) && (
                      <th className="p-2 text-center font-semibold">3P%</th>
                    )}
                    {players.every(p => p.stats.ftm !== undefined) && (
                      <th className="p-2 text-center font-semibold">FTM</th>
                    )}
                    {players.every(p => p.stats.fta !== undefined) && (
                      <th className="p-2 text-center font-semibold">FTA</th>
                    )}
                    {players.every(p => p.stats.ftp !== undefined) && (
                      <th className="p-2 text-center font-semibold">FT%</th>
                    )}
                    {players.every(p => p.stats.orb !== undefined) && (
                      <th className="p-2 text-center font-semibold">ORB</th>
                    )}
                    {players.every(p => p.stats.drb !== undefined) && (
                      <th className="p-2 text-center font-semibold">DRB</th>
                    )}
                    <th className="p-2 text-center font-semibold">RPG</th>
                    <th className="p-2 text-center font-semibold">APG</th>
                    <th className="p-2 text-center font-semibold">SPG</th>
                    <th className="p-2 text-center font-semibold">BPG</th>
                    {players.every(p => p.stats.tov !== undefined) && (
                      <th className="p-2 text-center font-semibold">TOV</th>
                    )}
                    {players.every(p => p.stats.pf !== undefined) && (
                      <th className="p-2 text-center font-semibold">PF</th>
                    )}
                    <th className="p-2 text-center font-semibold">PI</th>
                  </tr>
                </thead>
                <tbody>
                  {players.map((player, playerIndex) => (
                    <tr key={player.id} className="border-b hover:bg-muted/50">
                      <td className="p-2 font-medium sticky left-0 bg-background whitespace-nowrap">
                        {player.name} <span className="text-xs text-muted-foreground">({player.position})</span>
                      </td>
                      {players.every(p => p.stats.gp !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('gp') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.gp}
                        </td>
                      )}
                      {players.every(p => p.stats.mpg !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('mpg') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.mpg?.toFixed(1) || "-"}
                        </td>
                      )}
                      <td className={`p-2 text-center ${playerIndex === getBestStatIndex('ppg') ? 'text-green-500 font-bold' : ''}`}>
                        {player.stats.ppg.toFixed(1)}
                      </td>
                      {players.every(p => p.stats.fgm !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('fgm') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.fgm?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.stats.fga !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('fga') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.fga?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.stats.fg !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('fg') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.fg ? `${player.stats.fg.toFixed(1)}%` : "-"}
                        </td>
                      )}
                      {players.every(p => p.stats.tpm !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('tpm') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.tpm?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.stats.tpa !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('tpa') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.tpa?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.stats.tpp !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('tpp') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.tpp ? `${player.stats.tpp.toFixed(1)}%` : "-"}
                        </td>
                      )}
                      {players.every(p => p.stats.ftm !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('ftm') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.ftm?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.stats.fta !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('fta') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.fta?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.stats.ftp !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('ftp') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.ftp ? `${player.stats.ftp.toFixed(1)}%` : "-"}
                        </td>
                      )}
                      {players.every(p => p.stats.orb !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('orb') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.orb?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.stats.drb !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('drb') ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.drb?.toFixed(1) || "-"}
                        </td>
                      )}
                      <td className={`p-2 text-center ${playerIndex === getBestStatIndex('rpg') ? 'text-green-500 font-bold' : ''}`}>
                        {player.stats.rpg.toFixed(1)}
                      </td>
                      <td className={`p-2 text-center ${playerIndex === getBestStatIndex('apg') ? 'text-green-500 font-bold' : ''}`}>
                        {player.stats.apg.toFixed(1)}
                      </td>
                      <td className={`p-2 text-center ${playerIndex === getBestStatIndex('spg') ? 'text-green-500 font-bold' : ''}`}>
                        {player.stats.spg.toFixed(1)}
                      </td>
                      <td className={`p-2 text-center ${playerIndex === getBestStatIndex('bpg') ? 'text-green-500 font-bold' : ''}`}>
                        {player.stats.bpg.toFixed(1)}
                      </td>
                      {players.every(p => p.stats.tov !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('tov', false) ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.tov?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.stats.pf !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('pf', false) ? 'text-green-500 font-bold' : ''}`}>
                          {player.stats.pf?.toFixed(1) || "-"}
                        </td>
                      )}
                      <td className={`p-2 text-center ${playerIndex === getBestStatIndex('playerIndex') ? 'text-green-500 font-bold' : ''}`}>
                        {player.playerIndex === 0 ? "NR" : player.playerIndex.toFixed(1)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>

          {/* Offensive Stats Tab */}
          <TabsContent value="offensive" className="space-y-4">
            {/* Player Info Section */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="font-semibold">Player Info</div>
              {players.map((player) => (
                <div key={`${player.id}-info-off`} className="space-y-2">
                  <div className="font-bold text-lg">{player.name}</div>
                  <div className="flex items-center gap-2">
                    <Badge className="text-xs">{player.position}</Badge>
                    {player.transferPortal && (
                      <Badge
                        variant={player.status === 'Committed' ? "destructive" : "outline"}
                        className={`text-xs ${player.status === 'Committed' ? "" : "bg-green-600 hover:bg-green-700 text-white"}`}
                      >
                        {player.status}
                      </Badge>
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground">{player.team}</div>
                  <div className="text-sm text-muted-foreground">{player.height} · {player.weight} lbs</div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="text-sm">Player Index:</span>
                      <span className={`font-bold ${getIndexColorClass(player.playerIndex)}`}>
                        {player.playerIndex === 0 ? "NR" : player.playerIndex.toFixed(1)}
                      </span>
                    </div>
                    <Progress
                      value={player.playerIndex}
                      max={100}
                      className="h-2"
                      indicatorClassName={getProgressColorClass(player.playerIndex)}
                    />
                    <div className="grid grid-cols-4 gap-1 text-center mt-1 bg-muted/30 rounded-sm p-1">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <p className="text-xs font-medium">{player.advancedStats?.poi?.toFixed(1) || 'N/A'}</p>
                              <p className="text-[10px] text-muted-foreground">POI</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">POI is the PlayVision Offense Index</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <p className="text-xs font-medium">{player.advancedStats?.pdi?.toFixed(1) || 'N/A'}</p>
                              <p className="text-[10px] text-muted-foreground">PDI</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">PDI is the PlayVision Defense Index</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <p className="text-xs font-medium">{player.advancedStats?.pei?.toFixed(1) || 'N/A'}</p>
                              <p className="text-[10px] text-muted-foreground">PEI</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">PEI is the PlayVision Efficiency Index</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <p className="text-xs font-medium">{player.advancedStats?.pui?.toFixed(1) || 'N/A'}</p>
                              <p className="text-[10px] text-muted-foreground">PUI</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">PUI is the PlayVision Usage Index</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <hr className="my-4" />

            {/* Offensive Stats Table */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2 font-semibold sticky left-0 bg-background">Player</th>
                    {players.every(p => p.advancedStats?.ts_pct !== undefined) && (
                      <th className="p-2 text-center font-semibold">TS%</th>
                    )}
                    {players.every(p => p.advancedStats?.efg_pct !== undefined) && (
                      <th className="p-2 text-center font-semibold">eFG%</th>
                    )}
                    {players.every(p => p.advancedStats?.total_s_pct !== undefined) && (
                      <th className="p-2 text-center font-semibold">Total S%</th>
                    )}
                    {players.every(p => p.advancedStats?.ftr !== undefined) && (
                      <th className="p-2 text-center font-semibold">FT Rate</th>
                    )}
                    {players.every(p => p.advancedStats?.orb !== undefined) && (
                      <th className="p-2 text-center font-semibold">ORB%</th>
                    )}
                    {players.every(p => p.advancedStats?.ast !== undefined) && (
                      <th className="p-2 text-center font-semibold">AST%</th>
                    )}
                    {players.every(p => p.advancedStats?.usg !== undefined) && (
                      <th className="p-2 text-center font-semibold">USG%</th>
                    )}
                    {players.every(p => p.advancedStats?.ppr !== undefined) && (
                      <th className="p-2 text-center font-semibold">PPR</th>
                    )}
                    {players.every(p => p.advancedStats?.pps !== undefined) && (
                      <th className="p-2 text-center font-semibold">PPS</th>
                    )}
                    {players.every(p => p.advancedStats?.ortg !== undefined) && (
                      <th className="p-2 text-center font-semibold">ORtg</th>
                    )}
                    {players.every(p => p.advancedStats?.ediff !== undefined) && (
                      <th className="p-2 text-center font-semibold">eDiff</th>
                    )}
                    {players.every(p => p.advancedStats?.fic !== undefined) && (
                      <th className="p-2 text-center font-semibold">FIC</th>
                    )}
                    {players.every(p => p.advancedStats?.per !== undefined) && (
                      <th className="p-2 text-center font-semibold">PER</th>
                    )}
                    <th className="p-2 text-center font-semibold">PI</th>
                  </tr>
                </thead>
                <tbody>
                  {players.map((player, playerIndex) => (
                    <tr key={player.id} className="border-b hover:bg-muted/50">
                      <td className="p-2 font-medium sticky left-0 bg-background whitespace-nowrap">
                        {player.name} <span className="text-xs text-muted-foreground">({player.position})</span>
                      </td>
                      {players.every(p => p.advancedStats?.ts_pct !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('ts_pct') ? 'text-green-500 font-bold' : ''}`}>
                          {formatPercentage(player.advancedStats?.ts_pct || 0)}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.efg_pct !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('efg_pct') ? 'text-green-500 font-bold' : ''}`}>
                          {formatPercentage(player.advancedStats?.efg_pct || 0)}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.total_s_pct !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('total_s_pct') ? 'text-green-500 font-bold' : ''}`}>
                          {formatPercentage(player.advancedStats?.total_s_pct || 0)}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.ftr !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('ftr') ? 'text-green-500 font-bold' : ''}`}>
                          {player.advancedStats?.ftr?.toFixed(2) || "-"}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.orb_pct !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('orb_pct') ? 'text-green-500 font-bold' : ''}`}>
                          {formatPercentage(player.advancedStats?.orb_pct || 0)}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.ast_pct !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('ast_pct') ? 'text-green-500 font-bold' : ''}`}>
                          {formatPercentage(player.advancedStats?.ast_pct || 0)}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.usg_pct !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('usg_pct') ? 'text-green-500 font-bold' : ''}`}>
                          {formatPercentage(player.advancedStats?.usg_pct || 0)}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.ppr !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('ppr') ? 'text-green-500 font-bold' : ''}`}>
                          {player.advancedStats?.ppr?.toFixed(2) || "-"}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.pps !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('pps') ? 'text-green-500 font-bold' : ''}`}>
                          {player.advancedStats?.pps?.toFixed(2) || "-"}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.ortg !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('ortg') ? 'text-green-500 font-bold' : ''}`}>
                          {player.advancedStats?.ortg?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.ediff !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('ediff') ? 'text-green-500 font-bold' : ''}`}>
                          {player.advancedStats?.ediff?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.fic !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('fic') ? 'text-green-500 font-bold' : ''}`}>
                          {player.advancedStats?.fic?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.per !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('per') ? 'text-green-500 font-bold' : ''}`}>
                          {player.advancedStats?.per?.toFixed(1) || "-"}
                        </td>
                      )}
                      <td className={`p-2 text-center ${playerIndex === getBestStatIndex('playerIndex') ? 'text-green-500 font-bold' : ''}`}>
                        {player.playerIndex === 0 ? "NR" : player.playerIndex.toFixed(1)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>

          {/* Defensive Stats Tab */}
          <TabsContent value="defensive" className="space-y-4">
            {/* Player Info Section */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="font-semibold">Player Info</div>
              {players.map((player) => (
                <div key={`${player.id}-info-def`} className="space-y-2">
                  <div className="font-bold text-lg">{player.name}</div>
                  <div className="flex items-center gap-2">
                    <Badge className="text-xs">{player.position}</Badge>
                    {player.transferPortal && (
                      <Badge
                        variant={player.status === 'Committed' ? "destructive" : "outline"}
                        className={`text-xs ${player.status === 'Committed' ? "" : "bg-green-600 hover:bg-green-700 text-white"}`}
                      >
                        {player.status}
                      </Badge>
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground">{player.team}</div>
                  <div className="text-sm text-muted-foreground">{player.height} · {player.weight} lbs</div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="text-sm">Player Index:</span>
                      <span className={`font-bold ${getIndexColorClass(player.playerIndex)}`}>
                        {player.playerIndex === 0 ? "NR" : player.playerIndex.toFixed(1)}
                      </span>
                    </div>
                    <Progress
                      value={player.playerIndex}
                      max={100}
                      className="h-2"
                      indicatorClassName={getProgressColorClass(player.playerIndex)}
                    />
                    <div className="grid grid-cols-4 gap-1 text-center mt-1 bg-muted/30 rounded-sm p-1">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <p className="text-xs font-medium">{player.advancedStats?.poi?.toFixed(1) || 'N/A'}</p>
                              <p className="text-[10px] text-muted-foreground">POI</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">POI is the PlayVision Offense Index</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <p className="text-xs font-medium">{player.advancedStats?.pdi?.toFixed(1) || 'N/A'}</p>
                              <p className="text-[10px] text-muted-foreground">PDI</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">PDI is the PlayVision Defense Index</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <p className="text-xs font-medium">{player.advancedStats?.pei?.toFixed(1) || 'N/A'}</p>
                              <p className="text-[10px] text-muted-foreground">PEI</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">PEI is the PlayVision Efficiency Index</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <p className="text-xs font-medium">{player.advancedStats?.pui?.toFixed(1) || 'N/A'}</p>
                              <p className="text-[10px] text-muted-foreground">PUI</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">PUI is the PlayVision Usage Index</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <hr className="my-4" />

            {/* Defensive Stats Table */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2 font-semibold sticky left-0 bg-background">Player</th>
                    {players.every(p => p.advancedStats?.drb !== undefined) && (
                      <th className="p-2 text-center font-semibold">DRB%</th>
                    )}
                    {players.every(p => p.advancedStats?.trb !== undefined) && (
                      <th className="p-2 text-center font-semibold">TRB%</th>
                    )}
                    {players.every(p => p.advancedStats?.tov !== undefined) && (
                      <th className="p-2 text-center font-semibold">TOV%</th>
                    )}
                    {players.every(p => p.advancedStats?.stl !== undefined) && (
                      <th className="p-2 text-center font-semibold">STL%</th>
                    )}
                    {players.every(p => p.advancedStats?.blk !== undefined) && (
                      <th className="p-2 text-center font-semibold">BLK%</th>
                    )}
                    {players.every(p => p.advancedStats?.drtg !== undefined) && (
                      <th className="p-2 text-center font-semibold">DRtg</th>
                    )}
                    {players.every(p => p.advancedStats?.ediff !== undefined) && (
                      <th className="p-2 text-center font-semibold">eDiff</th>
                    )}
                    {players.every(p => p.advancedStats?.fic !== undefined) && (
                      <th className="p-2 text-center font-semibold">FIC</th>
                    )}
                    {players.every(p => p.advancedStats?.per !== undefined) && (
                      <th className="p-2 text-center font-semibold">PER</th>
                    )}
                    <th className="p-2 text-center font-semibold">PI</th>
                  </tr>
                </thead>
                <tbody>
                  {players.map((player, playerIndex) => (
                    <tr key={player.id} className="border-b hover:bg-muted/50">
                      <td className="p-2 font-medium sticky left-0 bg-background whitespace-nowrap">
                        {player.name} <span className="text-xs text-muted-foreground">({player.position})</span>
                      </td>
                      {players.every(p => p.advancedStats?.drb !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('drb') ? 'text-green-500 font-bold' : ''}`}>
                          {formatPercentage(player.advancedStats?.drb || 0)}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.trb !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('trb') ? 'text-green-500 font-bold' : ''}`}>
                          {formatPercentage(player.advancedStats?.trb || 0)}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.tov !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('tov', false) ? 'text-green-500 font-bold' : ''}`}>
                          {formatPercentage(player.advancedStats?.tov || 0)}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.stl !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('stl') ? 'text-green-500 font-bold' : ''}`}>
                          {formatPercentage(player.advancedStats?.stl || 0)}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.blk !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('blk') ? 'text-green-500 font-bold' : ''}`}>
                          {formatPercentage(player.advancedStats?.blk || 0)}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.drtg !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('drtg', false) ? 'text-green-500 font-bold' : ''}`}>
                          {player.advancedStats?.drtg?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.ediff !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('ediff') ? 'text-green-500 font-bold' : ''}`}>
                          {player.advancedStats?.ediff?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.fic !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('fic') ? 'text-green-500 font-bold' : ''}`}>
                          {player.advancedStats?.fic?.toFixed(1) || "-"}
                        </td>
                      )}
                      {players.every(p => p.advancedStats?.per !== undefined) && (
                        <td className={`p-2 text-center ${playerIndex === getBestStatIndex('per') ? 'text-green-500 font-bold' : ''}`}>
                          {player.advancedStats?.per?.toFixed(1) || "-"}
                        </td>
                      )}
                      <td className={`p-2 text-center ${playerIndex === getBestStatIndex('playerIndex') ? 'text-green-500 font-bold' : ''}`}>
                        {player.playerIndex === 0 ? "NR" : player.playerIndex.toFixed(1)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
