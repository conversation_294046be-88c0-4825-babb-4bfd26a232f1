"use client"

import { useState } from "react"
import { <PERSON> } from "@/types/player"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Bar, BarChart, Line, LineChart, Radar, RadarChart, PolarAngleAxis, PolarGrid } from "recharts"
import { Badge } from "@/components/ui/badge"

interface PlayerComparisonChartProps {
  players: Player[]
}

// Define the valid stat keys based on the Player stats structure
type StatKeys = keyof Player['stats'];

export function PlayerComparisonChart({ players }: PlayerComparisonChartProps) {
  const [chartType, setChartType] = useState<"bar" | "radar">("radar")
  const [statCategory, setStatCategory] = useState<"basic" | "shooting" | "advanced">("basic")
  
  // Define stat options for each category
  const statOptions = {
    basic: [
      { key: "ppg", label: "Points Per Game" },
      { key: "rpg", label: "Rebounds Per Game" },
      { key: "apg", label: "Assists Per Game" },
      { key: "spg", label: "Steals Per Game" },
      { key: "bpg", label: "Blocks Per Game" },
    ],
    shooting: [
      { key: "fg", label: "Field Goal %" },
      { key: "tpp", label: "Three Point %" },
      { key: "ftp", label: "Free Throw %" },
    ],
    advanced: [
      { key: "ts", label: "True Shooting %" },
      { key: "efg", label: "Effective FG %" },
      { key: "usg", label: "Usage Rate" },
      { key: "per", label: "PER" },
      { key: "ortg", label: "Offensive Rating" },
      { key: "drtg", label: "Defensive Rating" },
    ]
  };

  // Player colors for charts
  const playerColors = [
    "hsl(var(--chart-1))",
    "hsl(var(--chart-2))",
    "hsl(var(--chart-3))",
    "hsl(var(--chart-4))",
  ];

  // Prepare data for radar chart
  const radarData = statOptions[statCategory].map(stat => {
    const dataPoint: any = { stat: stat.label };
    
    players.forEach((player, index) => {
      let value;
      
      if (statCategory === "advanced" && player.advancedStats) {
        value = player.advancedStats[stat.key as keyof typeof player.advancedStats];
      } else {
        value = player.stats[stat.key as keyof Player['stats']];
      }
      
      dataPoint[`player${index}`] = value || 0;
    });
    
    return dataPoint;
  });

  // Prepare data for bar chart
  const barData = players.map((player, index) => {
    const dataPoint: any = { 
      name: player.name.split(" ")[1] || player.name, // Use last name for brevity
      fullName: player.name,
    };
    
    statOptions[statCategory].forEach(stat => {
      let value;
      
      if (statCategory === "advanced" && player.advancedStats) {
        value = player.advancedStats[stat.key as keyof typeof player.advancedStats];
      } else {
        value = player.stats[stat.key as keyof Player['stats']];
      }
      
      dataPoint[stat.key] = value || 0;
    });
    
    return dataPoint;
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle>Player Statistics Comparison</CardTitle>
            <CardDescription>Visual comparison of player performance metrics</CardDescription>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Select value={statCategory} onValueChange={(value) => setStatCategory(value as any)}>
              <SelectTrigger className="w-full sm:w-44">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="basic">Basic Stats</SelectItem>
                <SelectItem value="shooting">Shooting Stats</SelectItem>
                <SelectItem value="advanced">Advanced Stats</SelectItem>
              </SelectContent>
            </Select>
            
            <Tabs value={chartType} onValueChange={setChartType as any} className="w-full sm:w-44">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="bar">Bar</TabsTrigger>
                <TabsTrigger value="radar">Radar</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2 mt-4">
          {players.map((player, index) => (
            <div key={player.id} className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: playerColors[index % playerColors.length] }}
              />
              <span className="text-sm font-medium">{player.name}</span>
              <Badge className="text-xs">{player.position}</Badge>
            </div>
          ))}
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[500px] w-full">
          <ChartContainer
            config={
              chartType === "radar" 
                ? players.reduce((config, _, index) => {
                    config[`player${index}`] = { 
                      label: players[index].name, 
                      color: playerColors[index % playerColors.length] 
                    };
                    return config;
                  }, {} as Record<string, { label: string; color: string }>)
                : statOptions[statCategory].reduce((config, stat) => {
                    config[stat.key] = { 
                      label: stat.label, 
                      color: "hsl(var(--primary))" 
                    };
                    return config;
                  }, {} as Record<string, { label: string; color: string }>)
            }
          >
            {chartType === "radar" ? (
              <RadarChart data={radarData} outerRadius={150}>
                <PolarGrid />
                <PolarAngleAxis dataKey="stat" />
                <ChartTooltip content={<ChartTooltipContent />} />
                {players.map((player, index) => (
                  <Radar
                    key={player.id}
                    name={player.name}
                    dataKey={`player${index}`}
                    stroke={playerColors[index % playerColors.length]}
                    fill={playerColors[index % playerColors.length]}
                    fillOpacity={0.2}
                  />
                ))}
              </RadarChart>
            ) : (
              <BarChart data={barData} layout="vertical">
                <ChartTooltip content={<ChartTooltipContent />} />
                {statOptions[statCategory].map((stat) => (
                  <Bar 
                    key={stat.key}
                    dataKey={stat.key} 
                    radius={[0, 4, 4, 0]} 
                    className="fill-primary"
                  />
                ))}
              </BarChart>
            )}
          </ChartContainer>
        </div>
      </CardContent>
    </Card>
  )
}
