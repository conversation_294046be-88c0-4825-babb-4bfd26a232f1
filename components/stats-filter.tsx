"use client"
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RotateCcw } from "lucide-react"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { HeightDropdownFilter } from "@/components/height-dropdown-filter"
import { AdvancedStatsFilter } from "@/components/advanced-stats-filter"
import { TeamSelect } from "@/components/team-select"
import { ConferenceSelect } from "@/components/conference-select"
import { PositionSelect } from "@/components/position-select"
import { ClassSelect } from "@/components/class-select"

import { FilterCriteria } from "@/components/stats-filter-page"

type NumericFilterKey = keyof {
  [K in keyof FilterCriteria as FilterCriteria[K] extends number ? K : never]: FilterCriteria[K]
} | 'minPlayerIndex' | 'maxPlayerIndex'

interface StatsFilterProps {
  onFilterChange: (filters: FilterCriteria) => void
  filterRanges?: Partial<FilterCriteria>
  onClose?: () => void
  onReset?: () => void
}

export function StatsFilter({ onFilterChange, filterRanges, onClose, onReset }: StatsFilterProps) {
  // Local state for filters that will be applied when the user clicks "Apply"
  const [filters, setFilters] = useState<FilterCriteria>({
    positions: [],
    teams: [],
    conferences: [],
    classes: [],
    // Physical
    minHeight: 0,
    maxHeight: 88,
    minWeight: 0,
    maxWeight: 350,
    // Games
    minGP: 0,
    maxGP: 40,
    minMPG: 0,
    maxMPG: 40,
    // Scoring
    minPPG: 0,
    maxPPG: 35,
    minFGM: 0,
    maxFGM: 15,
    minFGA: 0,
    maxFGA: 30,
    minFGP: 0,
    maxFGP: 100,
    min3PM: 0,
    max3PM: 10,
    min3PA: 0,
    max3PA: 20,
    min3PP: 0,
    max3PP: 100,
    minFTM: 0,
    maxFTM: 15,
    minFTA: 0,
    maxFTA: 20,
    minFTP: 0,
    maxFTP: 100,
    // Rebounds
    minORB: 0,
    maxORB: 10,
    minDRB: 0,
    maxDRB: 10,
    minRPG: 0,
    maxRPG: 15,
    // Other Stats
    minAPG: 0,
    maxAPG: 10,
    minSPG: 0,
    maxSPG: 5,
    minBPG: 0,
    maxBPG: 5,
    minTOV: 0,
    maxTOV: 5,
    minPF: 0,
    maxPF: 8,
    // Player Index
    minPlayerIndex: 0,
    maxPlayerIndex: 100,
    // Offensive Advanced Stats
    minTS: 0,
    maxTS: 150,
    minEFG: 0,
    maxEFG: 150,
    minTotalS: 0,
    maxTotalS: 300,
    minORBPct: 0,
    maxORBPct: 100,
    minASTPct: 0,
    maxASTPct: 100,
    minTOVPct: 0,
    maxTOVPct: 100,
    minUSG: 0,
    maxUSG: 105,
    minPPR: -125,
    maxPPR: 75,
    minPPS: 0,
    maxPPS: 5,
    minORtg: -1000,
    maxORtg: 300,
    minFTRate: 0,
    maxFTRate: 200,
    minFIC: -10,
    maxFIC: 800,
    minPER: -65,
    maxPER: 200,
    // Defensive Advanced Stats
    minDRBPct: 0,
    maxDRBPct: 120,
    minTRBPct: 0,
    maxTRBPct: 100,
    minSTLPct: 0,
    maxSTLPct: 100,
    minBLKPct: 0,
    maxBLKPct: 200,
    minDRtg: -100,
    maxDRtg: 160,
    minEDiff: -1000,
    maxEDiff: 300,
    // Other filters (neutral defaults)
    active: false,
    statusEntered: false,
    statusCommitted: false,
    notRated: true,
    searchTerm: "",
    dateFrom: "",
    dateTo: ""
  })

  // State to track the applied filters
  const [appliedFilters, setAppliedFilters] = useState<FilterCriteria>(filters);

  // We only propagate when user presses Apply or Reset

  // Apply current filters
  const handleApplyFilters = () => {
    setAppliedFilters({...filters});
    onFilterChange({...filters});
  }

  const handleReset = () => {
    const resetFilters = {
      positions: [],
      teams: [],
      conferences: [],
      classes: [],
      // Physical
      minHeight: 0,
      maxHeight: 88,
      minWeight: 0,
      maxWeight: 350,
      // Games
      minGP: 0,
      maxGP: 40,
      minMPG: 0,
      maxMPG: 40,
      // Scoring
      minPPG: 0,
      maxPPG: 35,
      minFGM: 0,
      maxFGM: 15,
      minFGA: 0,
      maxFGA: 30,
      minFGP: 0,
      maxFGP: 100,
      min3PM: 0,
      max3PM: 10,
      min3PA: 0,
      max3PA: 20,
      min3PP: 0,
      max3PP: 100,
      minFTM: 0,
      maxFTM: 15,
      minFTA: 0,
      maxFTA: 20,
      minFTP: 0,
      maxFTP: 100,
      // Rebounds
      minORB: 0,
      maxORB: 10,
      minDRB: 0,
      maxDRB: 10,
      minRPG: 0,
      maxRPG: 15,
      // Other Stats
      minAPG: 0,
      maxAPG: 10,
      minSPG: 0,
      maxSPG: 5,
      minBPG: 0,
      maxBPG: 5,
      minTOV: 0,
      maxTOV: 5,
      minPF: 0,
      maxPF: 8,
      // Player Index
      minPlayerIndex: 0,
      maxPlayerIndex: 100,
      // Offensive Advanced Stats
      minTS: 0,
      maxTS: 150,
      minEFG: 0,
      maxEFG: 150,
      minTotalS: 0,
      maxTotalS: 300,
      minORBPct: 0,
      maxORBPct: 100,
      minASTPct: 0,
      maxASTPct: 100,
      minTOVPct: 0,
      maxTOVPct: 100,
      minUSG: 0,
      maxUSG: 105,
      minPPR: -125,
      maxPPR: 75,
      minPPS: 0,
      maxPPS: 5,
      minORtg: -1000,
      maxORtg: 300,
      minFTRate: 0,
      maxFTRate: 200,
      minFIC: -10,
      maxFIC: 800,
      minPER: -65,
      maxPER: 200,
      // Defensive Advanced Stats
      minDRBPct: 0,
      maxDRBPct: 120,
      minTRBPct: 0,
      maxTRBPct: 100,
      minSTLPct: 0,
      maxSTLPct: 100,
      minBLKPct: 0,
      maxBLKPct: 200,
      minDRtg: -100,
      maxDRtg: 160,
      minEDiff: -1000,
      maxEDiff: 300,
      // Other filters (neutral defaults)
      active: false,
      statusEntered: false,
      statusCommitted: false,
      notRated: true,
      searchTerm: "",
      dateFrom: "",
      dateTo: ""
    } as FilterCriteria

    setFilters(resetFilters);
    setAppliedFilters(resetFilters);
    onFilterChange(resetFilters);
    onReset?.();
  }

  // Component for numeric stat inputs with min/max
  const StatInputs = ({
    label,
    min,
    max,
    minKey,
    maxKey,
    step = 1,
    unit = "",
  }: {
    label: string
    min: number
    max: number
    minKey: NumericFilterKey
    maxKey: NumericFilterKey
    step?: number
    unit?: string
  }) => {
    // Use local state for input values to prevent re-renders during typing
    const [minValue, setMinValue] = useState<string>((filters[minKey] as number).toString());
    const [maxValue, setMaxValue] = useState<string>((filters[maxKey] as number).toString());

    // Update local state when filter values change from outside
    useEffect(() => {
      setMinValue((filters[minKey] as number).toString());
      setMaxValue((filters[maxKey] as number).toString());
    }, [filters[minKey], filters[maxKey]]);

    // Handle min input change - only update local state, not parent state
    const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setMinValue(e.target.value);
    };

    // Handle max input change - only update local state, not parent state
    const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setMaxValue(e.target.value);
    };

    // Update parent state only when input loses focus
    const handleMinBlur = () => {
      let value = minValue === "" ? min : Number(minValue);
      // Ensure value is within min/max bounds
      if (value < min) value = min;
      if (value > max) value = max;
      if (!isNaN(value)) {
        setFilters((prev) => ({ ...prev, [minKey]: value }));
        // Update the displayed value to match the constrained value
        setMinValue(value.toString());
      }
    };

    const handleMaxBlur = () => {
      let value = maxValue === "" ? max : Number(maxValue);
      // Ensure value is within min/max bounds
      if (value < min) value = min;
      if (value > max) value = max;
      if (!isNaN(value)) {
        setFilters((prev) => ({ ...prev, [maxKey]: value }));
        // Update the displayed value to match the constrained value
        setMaxValue(value.toString());
      }
    };

    // Handle focus to select all text for easier editing
    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      e.target.select();
    };

    return (
      <div className="space-y-1.5">
        <Label className="text-sm">{label}</Label>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label className="text-xs text-muted-foreground mb-0.5 block">Min</Label>
            <Input
              type="number"
              min={min}
              max={max}
              step={step}
              value={minValue}
              onChange={handleMinChange}
              onBlur={() => {
                handleMinBlur();
                // setAppliedFilters({...filters}); // Removed auto-apply
              }}
              onFocus={handleFocus}
              className="h-8"
            />
          </div>
          <div>
            <Label className="text-xs text-muted-foreground mb-0.5 block">Max</Label>
            <Input
              type="number"
              min={min}
              max={max}
              step={step}
              value={maxValue}
              onChange={handleMaxChange}
              onBlur={() => {
                handleMaxBlur();
                // setAppliedFilters({...filters}); // Removed auto-apply
              }}
              onFocus={handleFocus}
              className="h-8"
            />
          </div>
          {unit && <div className="text-sm">{unit}</div>}
        </div>
      </div>
    )
  }

  const [selectedPositions, setSelectedPositions] = useState<string[]>([])
  const [selectedTeams, setSelectedTeams] = useState<string[]>([])
  const [selectedConferences, setSelectedConferences] = useState<string[]>([])

  // Sync arrays into string fields for current backend filtering expectations
  useEffect(() => {
    setFilters(prev => ({ ...prev, positions: selectedPositions }))
  }, [selectedPositions])
  useEffect(() => {
    setFilters(prev => ({ ...prev, teams: selectedTeams }))
  }, [selectedTeams])
  useEffect(() => {
    setFilters(prev => ({ ...prev, conferences: selectedConferences }))
  }, [selectedConferences])

  return (
    <Card>
      <CardHeader className="space-y-1 py-3 px-4 sm:px-6 sticky top-0 left-0 right-0 z-30 bg-background border-b">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl">Filters</CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="default"
              size="sm"
              onClick={handleApplyFilters}
              className="h-8"
            >
              Apply
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              className="h-8 gap-1"
            >
              <RotateCcw className="h-3.5 w-3.5" />
              Reset
            </Button>
            {onClose && (
              <Button variant="ghost" size="sm" className="h-8" onClick={onClose} aria-label="Close filters">
                ×
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-2 px-4 sm:px-6 pt-3 pb-6">
        {/* Note: max-h computed by parent overlay; when embedded full-screen, container already fills to bottom */}

        {/* Top filters in compact grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
          <div className="space-y-1">
            <Label className="text-xs">Position</Label>
            <PositionSelect value={selectedPositions} onValueChange={setSelectedPositions} />
          </div>

          <div className="space-y-1">
            <Label className="text-xs">Class</Label>
            <div className="min-h-8">
              <ClassSelect
                value={filters.classes}
                onValueChange={(value) => {
                  setFilters((prev) => ({ ...prev, classes: value }))
                }}
              />
            </div>
          </div>

          <div className="space-y-1">
            <Label className="text-xs">Team</Label>
            <div className="min-h-8">
              <TeamSelect value={selectedTeams} onValueChange={setSelectedTeams} />
            </div>
          </div>

          <div className="space-y-1">
            <Label className="text-xs">Conference</Label>
            <div className="min-h-8">
              <ConferenceSelect value={selectedConferences} onValueChange={setSelectedConferences} />
            </div>
          </div>
        </div>

        {/* Transfer Portal Status row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div className="space-y-2">
            <Label>Transfer Portal</Label>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
              <div className="flex items-center gap-6">
                <label className="inline-flex items-center gap-2">
                  <Checkbox
                    id="statusEntered"
                    checked={filters.statusEntered}
                    onCheckedChange={(checked) => {
                      setFilters(prev => ({ ...prev, statusEntered: !!checked }))
                    }}
                  />
                  <span className="text-sm">Entered</span>
                </label>
                <label className="inline-flex items-center gap-2">
                  <Checkbox
                    id="statusCommitted"
                    checked={filters.statusCommitted}
                    onCheckedChange={(checked) => {
                      setFilters(prev => ({ ...prev, statusCommitted: !!checked }))
                    }}
                  />
                  <span className="text-sm">Committed</span>
                </label>
              </div>
              <div className="flex items-center gap-2 sm:ml-auto">
                <Label className="text-xs whitespace-nowrap">Date Range</Label>
                <Input
                  aria-label="Start Date"
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => setFilters((prev) => ({ ...prev, dateFrom: e.target.value }))}
                  className="h-8 w-[11rem]"
                />
                <Input
                  aria-label="End Date"
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => setFilters((prev) => ({ ...prev, dateTo: e.target.value }))}
                  className="h-8 w-[11rem]"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Stats Filter Tabs */}
        <Tabs defaultValue="basic" className="w-full mt-1">
          <TabsList className="grid grid-cols-3 w-full h-8">
            <TabsTrigger value="basic">Basic Stats</TabsTrigger>
            <TabsTrigger value="advanced">Advanced Stats</TabsTrigger>
            <TabsTrigger value="playvision">PlayVision</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="pt-3">
            <Tabs defaultValue="physical" className="w-full">
              <TabsList className="grid grid-cols-4 w-full h-8">
                <TabsTrigger value="physical">Physical</TabsTrigger>
                <TabsTrigger value="scoring">Scoring</TabsTrigger>
                <TabsTrigger value="rebounds">Rebounds</TabsTrigger>
                <TabsTrigger value="other">Other</TabsTrigger>
              </TabsList>

              <TabsContent value="physical" className="space-y-1.5 mt-2">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div className="md:col-span-1">
                    <HeightDropdownFilter
                      min={0}
                      max={88}
                      minKey="minHeight"
                      maxKey="maxHeight"
                      filters={filters}
                      setFilters={setFilters}
                    />
                  </div>
                  <StatInputs label="Weight (lbs)" min={0} max={300} minKey="minWeight" maxKey="maxWeight" />

                  <StatInputs
                    label="Games Played"
                    min={filterRanges?.minGP ?? 0}
                    max={filterRanges?.maxGP ?? 82}
                    minKey="minGP"
                    maxKey="maxGP"
                  />

                  <StatInputs label="Minutes Per Game" min={0} max={40} minKey="minMPG" maxKey="maxMPG" />
                </div>
              </TabsContent>

              <TabsContent value="scoring" className="space-y-2 mt-2">
                <Accordion type="multiple" defaultValue={[]}>
                  <AccordionItem value="ppg">
                    <AccordionTrigger>Points</AccordionTrigger>
                    <AccordionContent className="pt-2">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <StatInputs label="Points Per Game" min={0} max={35} minKey="minPPG" maxKey="maxPPG" />
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="fg">
                    <AccordionTrigger>Field Goals</AccordionTrigger>
                    <AccordionContent className="pt-2">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <StatInputs label="FG Made Per Game" min={0} max={15} minKey="minFGM" maxKey="maxFGM" />
                        <StatInputs label="FG Attempted Per Game" min={0} max={30} minKey="minFGA" maxKey="maxFGA" />
                        <StatInputs label="FG Percentage" min={0} max={100} minKey="minFGP" maxKey="maxFGP" unit="%" />
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="3p">
                    <AccordionTrigger>Three Pointers</AccordionTrigger>
                    <AccordionContent className="pt-2">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <StatInputs label="3P Made Per Game" min={0} max={10} minKey="min3PM" maxKey="max3PM" />
                        <StatInputs label="3P Attempted Per Game" min={0} max={20} minKey="min3PA" maxKey="max3PA" />
                        <StatInputs label="3P Percentage Per Game" min={0} max={100} minKey="min3PP" maxKey="max3PP" unit="%" />
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="ft">
                    <AccordionTrigger>Free Throws</AccordionTrigger>
                    <AccordionContent className="pt-2">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <StatInputs label="FT Made Per Game" min={0} max={15} minKey="minFTM" maxKey="maxFTM" />
                        <StatInputs label="FT Attempted Per Game" min={0} max={20} minKey="minFTA" maxKey="maxFTA" />
                        <StatInputs label="FT Percentage" min={0} max={100} minKey="minFTP" maxKey="maxFTP" unit="%" />
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </TabsContent>

              <TabsContent value="rebounds" className="space-y-2 mt-2">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <StatInputs label="Offensive Rebounds Per Game" min={0} max={10} minKey="minORB" maxKey="maxORB" step={0.1} />
                  <StatInputs label="Defensive Rebounds Per Game" min={0} max={10} minKey="minDRB" maxKey="maxDRB" step={0.1} />
                  <StatInputs label="Total Rebounds Per Game" min={0} max={15} minKey="minRPG" maxKey="maxRPG" step={0.1} />
                </div>
              </TabsContent>

              <TabsContent value="other" className="space-y-2 mt-2">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <StatInputs label="Assists Per Game" min={0} max={10} minKey="minAPG" maxKey="maxAPG" step={0.1} />
                  <StatInputs label="Steals Per Game" min={0} max={5} minKey="minSPG" maxKey="maxSPG" step={0.1} />
                  <StatInputs label="Blocks Per Game" min={0} max={5} minKey="minBPG" maxKey="maxBPG" step={0.1} />
                  <StatInputs label="Turnovers Per Game" min={0} max={10} minKey="minTOV" maxKey="maxTOV" step={0.1} />
                  <StatInputs label="Personal Fouls Per Game" min={0} max={6} minKey="minPF" maxKey="maxPF" step={0.1} />
                </div>
              </TabsContent>
            </Tabs>
          </TabsContent>

          <TabsContent value="advanced" className="pt-2">
            <AdvancedStatsFilter filters={filters} setFilters={setFilters} />
          </TabsContent>

          <TabsContent value="playvision" className="pt-2">
            <div className="space-y-2">
              {/* Player Index Inputs */}
              <div className="space-y-2">
                <StatInputs
                  label="Player Index"
                  min={filterRanges?.minPlayerIndex ?? 0}
                  max={100}
                  minKey="minPlayerIndex"
                  maxKey="maxPlayerIndex"
                />
                <div className="flex items-center space-x-2 mt-2">
                  <Checkbox
                    id="notRated"
                    checked={filters.notRated}
                    onCheckedChange={(checked) => {
                      setFilters(prev => ({ ...prev, notRated: !!checked }))
                    }}
                  />
                  <Label htmlFor="notRated" className="text-sm font-normal">
                    Show Not Rated Players (NR)
                  </Label>
                </div>
              </div>

              <div className="space-y-2 pt-2">
                <div className="space-y-2">
                  <Label>PlayVision Offense Index (POI)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        step={0.1}
                        value={filters.minPOI || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minPOI: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        step={0.1}
                        value={filters.maxPOI || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxPOI: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>PlayVision Defense Index (PDI)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        step={0.1}
                        value={filters.minPDI || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minPDI: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        step={0.1}
                        value={filters.maxPDI || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxPDI: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>PlayVision Efficiency Index (PEI)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        step={0.1}
                        value={filters.minPEI || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minPEI: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        step={0.1}
                        value={filters.maxPEI || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxPEI: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>PlayVision Usage Index (PUI)</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Min</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        step={0.1}
                        value={filters.minPUI || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, minPUI: value });
                        }}
                        className="h-8"
                      />
                    </div>
                    <div className="flex-1">
                      <Label className="text-xs text-muted-foreground mb-1 block">Max</Label>
                      <Input
                        type="number"
                        min={0}
                        max={100}
                        step={0.1}
                        value={filters.maxPUI || ""}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : Number(e.target.value);
                          setFilters({ ...filters, maxPUI: value });
                        }}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
