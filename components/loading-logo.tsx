"use client"

import Image from "next/image"

interface LoadingLogoProps {
  size?: number
  label?: string
}

export default function LoadingLogo({ size = 128, label }: LoadingLogoProps) {
  return (
    <div className="flex flex-col items-center justify-center gap-4 py-10">
      <div className="relative flex items-center justify-center" style={{ width: size * 1.2, height: size * 1.2 }}>
        {/* Pulsing gradient circle behind the logo */}
        <div
          aria-hidden
          className="absolute inset-0 rounded-full animate-glow-pulse"
          style={{
            background:
              "radial-gradient(closest-side, rgba(255,255,255,0.18), rgba(255,255,255,0.10) 55%, rgba(255,255,255,0.04) 75%, rgba(255,255,255,0) 100%)",
            filter: 'blur(0.3px)'
          }}
        />
        {/* Soft base circle */}
        <div className="absolute inset-3 rounded-full bg-muted/15" />

        {/* Centered logo */}
        <div className="relative" style={{ width: size, height: size }}>
          <Image
            src="/logo-trans.png"
            alt="PlayVision"
            width={size}
            height={size}
            className="rounded-md"
            priority
            unoptimized
          />
        </div>
      </div>
      {label && (
        <div className="text-sm text-muted-foreground">{label}</div>
      )}
    </div>
  )
} 