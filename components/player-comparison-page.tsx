"use client"

import { useState, useEffect } from "react"
import { <PERSON> } from "@/types/player"
import { getPlayerData } from "@/utils/data-transform"
import { PlayerSelectionPanel } from "@/components/player-selection-panel"
import { PlayerComparisonTable } from "@/components/player-comparison-table"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, ChevronDown } from "lucide-react"
import { useIndex } from "@/components/index-provider"
import { DropdownMenu, DropdownMenuContent, DropdownMenuLabel, DropdownMenuRadioGroup, DropdownMenuRadioItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"

export default function PlayerComparisonPage() {
  const [baselinePlayers, setBaselinePlayers] = useState<Player[]>([])
  const [allPlayers, setAllPlayers] = useState<Player[]>([])
  const [selectedPlayers, setSelectedPlayers] = useState<Player[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { applyIndexToPlayers, useCustomIndex, indexName, savedIndexes, setUseCustomIndex, applySavedIndex, useSeasonIndex, setUseSeasonIndex } = useIndex()

  // Load all players
  useEffect(() => {
    const loadPlayers = async () => {
      try {
        setIsLoading(true)
        const response = await getPlayerData()
        setBaselinePlayers(response.players)
        const applied = applyIndexToPlayers(response.players)
        setAllPlayers(applied)
      } catch (error) {
        console.error('Error loading players:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadPlayers()
  }, [applyIndexToPlayers])

  // Re-apply current index to the full list when weights/toggle change,
  // and sync selected players from the updated full list so normalization is global
  useEffect(() => {
    const updatedAll = applyIndexToPlayers(baselinePlayers)
    setAllPlayers(updatedAll)
    setSelectedPlayers((prevSelected) =>
      prevSelected.map((sp) => updatedAll.find((p) => p.id === sp.id) || sp)
    )
  }, [applyIndexToPlayers, baselinePlayers])

  const handleAddPlayer = (player: Player) => {
    if (selectedPlayers.length < 4 && !selectedPlayers.some(p => p.id === player.id)) {
      setSelectedPlayers([...selectedPlayers, player])
    }
  }

  const handleRemovePlayer = (playerId: string | number) => {
    setSelectedPlayers(selectedPlayers.filter(player => player.id !== playerId))
  }

  return (
    <div className="container px-4 sm:px-6 lg:px-8 py-6 mx-auto max-w-full">
      <div className="flex items-center justify-between mb-6 gap-3">
        <h1 className="text-3xl font-bold">Player Comparison</h1>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2">
              <span>{useCustomIndex ? `Custom: ${indexName}` : useSeasonIndex ? "Season" : "All-Time"}</span>
              <ChevronDown className="h-4 w-4 opacity-70" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Select Index</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuRadioGroup
              value={useCustomIndex ? indexName : useSeasonIndex ? "__season__" : "__alltime__"}
              onValueChange={(val) => {
                if (val === "__alltime__") {
                  setUseCustomIndex(false)
                  setUseSeasonIndex(false)
                } else if (val === "__season__") {
                  setUseCustomIndex(false)
                  setUseSeasonIndex(true)
                } else {
                  applySavedIndex(val)
                }
              }}
            >
              <DropdownMenuRadioItem value="__alltime__">All-Time</DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="__season__">Season</DropdownMenuRadioItem>
              <DropdownMenuSeparator />
              {savedIndexes.map((idx) => (
                <DropdownMenuRadioItem key={idx.name} value={idx.name}>{idx.name}</DropdownMenuRadioItem>
              ))}
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
        <div className="lg:col-span-1">
          <PlayerSelectionPanel
            allPlayers={allPlayers}
            selectedPlayers={selectedPlayers}
            onAddPlayer={handleAddPlayer}
            onRemovePlayer={handleRemovePlayer}
          />
        </div>

        <div className="lg:col-span-3">
          {selectedPlayers.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>No players selected</AlertTitle>
              <AlertDescription>
                Select up to 4 players from the panel on the left to compare their statistics.
              </AlertDescription>
            </Alert>
          ) : (
            <>
              <div className="mb-4">
                <h2 className="text-xl font-semibold">Comparing {selectedPlayers.length} Players</h2>
              </div>

              <PlayerComparisonTable players={selectedPlayers} />
            </>
          )}
        </div>
      </div>
    </div>
  )
}
