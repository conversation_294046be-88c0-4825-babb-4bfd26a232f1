"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Search } from "lucide-react"

export function StatsKey() {
  const playerSections: { title: string; items: { label: string; description: string }[] }[] = [
    {
      title: "Basic Stats",
      items: [
        { label: "GP", description: "Games Played." },
        { label: "MPG", description: "Minutes Per Game." },
        { label: "PPG", description: "Points Per Game." },
        { label: "ORB", description: "Offensive Rebounds per game." },
        { label: "DRB", description: "Defensive Rebounds per game." },
        { label: "RPG", description: "Total Rebounds per game." },
        { label: "APG", description: "Assists per game." },
        { label: "SPG", description: "Steals per game." },
        { label: "BPG", description: "Blocks per game." },
        { label: "TOV", description: "Turnovers per game." },
        { label: "PF", description: "Personal Fouls per game." },
      ],
    },
    {
      title: "Base Advanced Stats",
      items: [
        { label: "Min%", description: "% of team minutes the player was on court." },
        { label: "TS%", description: "True Shooting Percentage. Accounts for FG, 3P, and FT efficiency." },
        { label: "eFG%", description: "Effective Field Goal Percentage. Adjusts FG% to give more weight to 3-pointers." },
        { label: "Total S%", description: "Total Shooting Percentage. May refer to scoring efficiency weighted by usage." },
        { label: "ORB%", description: "Offensive Rebound Rate. Estimate of available offensive rebounds a player grabbed." },
        { label: "DRB%", description: "Defensive Rebound Rate." },
        { label: "TRB%", description: "Total Rebound Rate." },
        { label: "AST%", description: "Assist Rate. % of teammate field goals a player assisted while on court." },
        { label: "TOV%", description: "Turnover Rate. % of possessions ending in a turnover." },
        { label: "AST/TOV", description: "Assist-to-Turnover Ratio." },
        { label: "STL%", description: "Steal Rate. % of opponent possessions ending in a steal." },
        { label: "BLK%", description: "Block Rate." },
        { label: "USG%", description: "Usage Rate. % of team plays used by the player while on court." },
        { label: "PPR", description: "100 x (League Pace / Team Pace) x ([(Assists x 2/3) - Turnovers] / Minutes)." },
        { label: "PPS", description: "Points Per Shot." },
        { label: "ORtg", description: "Offensive Rating. Points produced per 100 possessions." },
        { label: "DRtg", description: "Defensive Rating. Points allowed per 100 possessions." },
        { label: "eDiff", description: "Efficiency Differential. ORtg minus DRtg." },
        { label: "FIC", description: "Floor Impact Counter. Overall productivity metric." },
        { label: "PORPAG", description: "Points Over Replacement Per Adjusted Game." },
        { label: "DPORPAG", description: "Defensive Points Over Replacement Per Adjusted Game." },
        { label: "Adj Off. Efficiency", description: "Adjusted Offensive Efficiency. Points per 100 possessions, adjusted for strength of schedule." },
        { label: "Adj Def. Efficiency", description: "Adjusted Defensive Rating. Defensive Points per 100 possessions, adjusted for strength of schedule." },
        { label: "stops", description: "Estimated defensive stops contributed." },
        { label: "PER", description: "Player Efficiency Rating. Per-minute rating of a player's performance." },
        { label: "Personal Foul Rate", description: "Fouls committed per minute or per 100 possessions." },
      ],
    },
    {
      title: "Shooting",
      items: [
        { label: "FGM", description: "Field Goals Made per game." },
        { label: "FGA", description: "Field Goals Attempted per game." },
        { label: "FG%", description: "Field Goal Percentage." },
        { label: "3PM", description: "Three-Point Field Goals Made per game." },
        { label: "3PA", description: "Three-Point Attempts per game." },
        { label: "3P%", description: "Three-Point Percentage." },
        { label: "FTM", description: "Free Throws Made per game." },
        { label: "FTA", description: "Free Throws Attempted per game." },
        { label: "FT%", description: "Free Throw Percentage." },
        { label: "FTR", description: "Free Throw Rate. Ratio of FTA to FGA." },
        { label: "2PM", description: "2-Point Field Goals Made." },
        { label: "2PA", description: "2-Point Field Goals Attempted." },
        { label: "2P%", description: "2-Point Field Goal Percentage." },
        { label: "Rim Makes", description: "Field Goals Made at the Rim." },
        { label: "Rim Attempts", description: "Field Goals Attempted at the Rim." },
        { label: "Mid-Range Makes", description: "Mid-Range Field Goals Made." },
        { label: "Mid-Range Attempts", description: "Mid-Range Field Goals Attempts." },
        { label: "Rim%", description: "Rim FG%." },
        { label: "Mid-Range%", description: "Mid-Range FG%." },
        { label: "3p/100", description: "3-Point Attempts per 100 possessions." },
      ],
    },
    {
      title: "Box Plus-Minus Metrics",
      items: [
        { label: "bpm", description: "Box Plus-Minus." },
        { label: "obpm", description: "Offensive Box Plus-Minus." },
        { label: "dbpm", description: "Defensive Box Plus-Minus." },
        { label: "gbpm", description: "Generic Box Plus-Minus." },
        { label: "ogbpm", description: "Offensive GBPM." },
        { label: "dgbpm", description: "Defensive GBPM." },
      ],
    },
  ]

  const teamSections: { title: string; items: { label: string; description: string }[] }[] = [
    {
      title: "Basic Info",
      items: [
        { label: "Team", description: "Name of the college basketball team." },
        { label: "Conference", description: "Team's athletic conference." },
      ],
    },
    {
      title: "Basic Stats",
      items: [
        { label: "GP", description: "Games Played." },
        { label: "MPG", description: "Minutes Per Game." },
        { label: "PPG", description: "Points Per Game." },
        { label: "ORB", description: "Offensive Rebounds per game." },
        { label: "DRB", description: "Defensive Rebounds per game." },
        { label: "RPG", description: "Total Rebounds per game." },
        { label: "APG", description: "Assists per game." },
        { label: "SPG", description: "Steals per game." },
        { label: "BPG", description: "Blocks per game." },
        { label: "TOV", description: "Turnovers per game." },
        { label: "PF", description: "Personal Fouls per game." },
      ],
    },
    {
      title: "Base Advanced Stats",
      items: [
        { label: "TS%", description: "True Shooting Percentage. Adjusts for FG, 3P, and FT value." },
        { label: "eFG%", description: "Effective Field Goal Percentage. Weighs 3PM more heavily." },
        { label: "FTR", description: "Free Throw Rate. FTA divided by FGA." },
        { label: "FTR Def", description: "Opponent Free Throw Rate." },
        { label: "3P Rate", description: "% of team's FGA that are three-pointers." },
        { label: "3P Rate Def.", description: "% of opponent FGA that are threes." },
        { label: "Total S%", description: "Total Shooting Percentage. Reflects scoring efficiency." },
        { label: "ORB%", description: "Offensive Rebound Rate. % of available ORB secured." },
        { label: "DRB%", description: "Defensive Rebound Rate." },
        { label: "TRB%", description: "Total Rebound Rate." },
        { label: "AST%", description: "Assist Rate. % of made FGs that were assisted." },
        { label: "TOV%", description: "Turnover Rate. % of possessions ending in a turnover." },
        { label: "Def. AST%", description: "% of opponent FGs that were assisted." },
        { label: "Def. TO%", description: "Turnovers forced per possession." },
        { label: "STL%", description: "Steal Rate." },
        { label: "BLK%", description: "Block Rate." },
        { label: "BLKED%", description: "% of team FGs that were blocked." },
        { label: "PPS", description: "Points Per Shot." },
        { label: "FIC40", description: "Floor Impact Counter per 40 minutes." },
        { label: "Poss", description: "Estimated Possessions per game." },
        { label: "Pace", description: "Possessions per 40 minutes." },
      ],
    },
    {
      title: "Offensive Shooting Metrics",
      items: [
        { label: "FGM", description: "Field Goals Made per game." },
        { label: "FGA", description: "Field Goals Attempted per game." },
        { label: "FG%", description: "Field Goal Percentage." },
        { label: "2P%", description: "Team 2-Point FG%." },
        { label: "3PM", description: "Three-Point Field Goals Made per game." },
        { label: "3PA", description: "Three-Point Field Goal Attempts per game." },
        { label: "3P%", description: "Three-Point Percentage." },
        { label: "FTM", description: "Free Throws Made per game." },
        { label: "FTA", description: "Free Throws Attempted per game." },
        { label: "FT%", description: "Free Throw Percentage." },
        { label: "Off Dunks FG%", description: "FG% on dunks by the offense." },
        { label: "Off. Dunk Share", description: "% of made FGs that were dunks." },
        { label: "Off. Rim FG%", description: "Team FG% at the rim." },
        { label: "Off. Rim. Share", description: "% of team FGA from the rim." },
        { label: "Off Mid Range FG%", description: "FG% on mid-range shots." },
        { label: "Off. Mid Range Share", description: "% of team FGA from mid-range." },
        { label: "Off 3P FG%", description: "Team 3-Point FG%." },
        { label: "Off 3P Share", description: "% of team FGA that are 3s." },
      ],
    },
    {
      title: "Defensive Shooting Metrics",
      items: [
        { label: "Def. eFG%", description: "Opponent Effective Field Goal %." },
        { label: "Def. 2P%", description: "Opponent 2-Point FG%." },
        { label: "Def. FT%", description: "Opponent Free Throw %." },
        { label: "Def Dunks FG%", description: "Opponent FG% on dunks." },
        { label: "Def. Dunk Share", description: "% of opponent made FGs that were dunks." },
        { label: "Def. Rim FG%", description: "Opponent FG% at the rim." },
        { label: "Def. Rim Share", description: "% of opponent FGA from the rim." },
        { label: "Def. Mid Range FG%", description: "Opponent mid-range FG%." },
        { label: "Def. Mid Range Share", description: "% of opponent FGA from mid-range." },
        { label: "Def. 3P FG%", description: "Opponent 3-Point FG%." },
        { label: "Def. 3P Share", description: "% of opponent FGA that are 3s." },
      ],
    },
    {
      title: "Other Advanced Stats, Adjusted Ratings, & Strength of Schedule",
      items: [
        { label: "Adj. Off. Efficiency", description: "Team's offensive efficiency adjusted for opponent." },
        { label: "Adj. Def. Efficiency", description: "Defensive efficiency adjusted for opponent." },
        { label: "BARTHAG", description: "Power rating estimating win probability vs average D-I team." },
        { label: "Strength of Schedule (SOS)", description: "Overall difficulty of team's schedule." },
        { label: "Non Conference SOS", description: "Strength of schedule for non-conference games." },
        { label: "Conference SOS", description: "Strength of conference opponents." },
        { label: "Opp OE", description: "Opponents' average Offensive Efficiency." },
        { label: "Opp DE", description: "Opponents' average Defensive Efficiency." },
      ],
    },
  ]

  type StatRow = { label: string; description: string; type: "Player" | "Team" }

  const allStats: StatRow[] = useMemo(() => {
    const playerFlat = playerSections.flatMap((section) =>
      section.items.map((item) => ({ label: item.label, description: item.description, type: "Player" as const }))
    )
    const teamFlat = teamSections.flatMap((section) =>
      section.items.map((item) => ({ label: item.label, description: item.description, type: "Team" as const }))
    )
    return [...playerFlat, ...teamFlat].sort((a, b) => a.label.localeCompare(b.label) || a.type.localeCompare(b.type))
  }, [])

  const [query, setQuery] = useState("")

  const filteredStats = useMemo(() => {
    if (!query.trim()) return allStats
    const q = query.toLowerCase()
    return allStats.filter((s) => s.label.toLowerCase().includes(q) || s.description.toLowerCase().includes(q) || s.type.toLowerCase().includes(q))
  }, [allStats, query])

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col gap-3 sm:flex-row sm:items-end sm:justify-between">
          <div>
            <CardTitle>Stats Key</CardTitle>
          </div>
          <div className="relative w-full sm:w-80">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search stats (e.g., TS%, Usage, Team)"
              className="pl-8"
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[65vh] pr-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-40">Stat</TableHead>
                <TableHead className="w-28">Type</TableHead>
                <TableHead>Definition</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStats.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={3} className="text-center text-muted-foreground">
                    No matching stats. Try a different search.
                  </TableCell>
                </TableRow>
              ) : (
                filteredStats.map((row) => (
                  <TableRow key={`${row.type}-${row.label}`}>
                    <TableCell className="font-medium">{row.label}</TableCell>
                    <TableCell>
                      <Badge variant="secondary">{row.type}</Badge>
                    </TableCell>
                    <TableCell className="text-muted-foreground">{row.description}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </ScrollArea>
      </CardContent>
    </Card>
  )
} 