"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er } from "@/components/ui/card"
import { <PERSON>ge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON>, Info } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Player } from "@/types/player"
import { PlayerStatsModal } from "@/components/player-stats-modal"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useIndex } from "@/components/index-provider"

const positionColors = {
  PG: "bg-blue-500",
  SG: "bg-green-500",
  SF: "bg-yellow-500",
  PF: "bg-orange-500",
  C: "bg-red-500",
  F: "bg-purple-500",
  G: "bg-pink-500",
  GF: "bg-gray-500",
  FC: "bg-teal-500",
} as const;

interface PlayerCardProps {
  player: Player;
  onSelect: () => void;
  isSelected: boolean;
  rank?: number;
}

export function PlayerCard({ player, onSelect, isSelected, rank }: PlayerCardProps) {
  const { useCustomIndex, indexName, useSeasonIndex } = useIndex()
  // Function to format date string (MM/DD/YYYY or YYYY-MM-DD) to a more readable format
  const formatDate = (dateString: string): string => {
    if (!dateString) return '';

    // Try to parse the date
    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      // If parsing fails, return the original string
      return dateString;
    }

    // Format the date as Month Day, Year
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Function to determine the color class based on the player index value
  const getIndexColorClass = (index: number) => {
    if (index >= 90) return "text-green-600 dark:text-green-400"
    if (index >= 80) return "text-emerald-500 dark:text-emerald-400"
    if (index >= 70) return "text-cyan-500 dark:text-cyan-300"
    if (index >= 60) return "text-sky-500 dark:text-sky-300"
    if (index >= 50) return "text-yellow-500 dark:text-yellow-300"
    if (index >= 40) return "text-amber-500 dark:text-amber-300"
    if (index >= 30) return "text-orange-500 dark:text-orange-300"
    if (index >= 20) return "text-rose-500 dark:text-rose-300"
    return "text-red-600 dark:text-red-400"
  }


  // Function to determine the progress color based on the player index value
  const getProgressColorClass = (index: number) => {
    if (index >= 90) return "bg-green-600"
    if (index >= 80) return "bg-emerald-500"
    if (index >= 70) return "bg-cyan-500"
    if (index >= 60) return "bg-sky-500"
    if (index >= 50) return "bg-yellow-500"
    if (index >= 40) return "bg-amber-500"
    if (index >= 30) return "bg-orange-500"
    if (index >= 20) return "bg-rose-500"
    return "bg-red-600"
  }

  return (
    <Card className={`cursor-pointer transition-all duration-200 hover:shadow-lg focus:outline-none focus-visible:outline-none`} onClick={onSelect}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-bold text-lg">{player.name}</h3>
            <p className="text-xs text-muted-foreground">
              {player.team} · {player.class} · {player.height} · {player.weight} lbs
            </p>
          </div>
          <div className="flex flex-col items-end gap-1">
            <div className="flex items-center gap-2">
              <Badge className={player.position ? positionColors[player.position] : 'bg-gray-500'}>{player.position || 'N/A'}</Badge>
              {player.starred && <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />}
            </div>
            {player.transferPortal && (
              <Badge
                variant={player.status === 'Committed' ? "destructive" : "outline"}
                className={`text-xs px-1 py-0 h-5 ${player.status === 'Committed' ? "" : "bg-green-600 hover:bg-green-700 text-white"}`}
              >
                {player.status === 'Committed' ? 'Committed' : 'Transfer'}
              </Badge>
            )}
          </div>
        </div>
        <div className="flex flex-wrap gap-2 mt-2">
          {player.transferPortal && player.dateEntered && (
            <p className="text-xs text-muted-foreground w-full">
              {player.status === 'Committed' ? 'Committed' : 'Entered'} on {formatDate(player.dateEntered)}
            </p>
          )}
        </div>
      </CardHeader>
      <CardContent className="pb-4 space-y-4">
        {/* Player Index Section */}
        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">
              {useCustomIndex 
                ? `Custom: ${indexName}` 
                : useSeasonIndex 
                  ? 'Season PPI'
                  : 'All-Time PPI'
              }
            </span>
            <span className={`text-lg font-bold ${getIndexColorClass(
              useCustomIndex 
                ? (player.playerIndex || 0)
                : useSeasonIndex
                  ? (player.scores?.season_ppi || player.playerIndex || 0)
                  : (player.scores?.all_time_ppi || player.playerIndex || 0)
            )}`}>
              {(() => {
                const indexValue = useCustomIndex 
                  ? (player.playerIndex || 0)
                  : useSeasonIndex
                    ? (player.scores?.season_ppi || player.playerIndex || 0)
                    : (player.scores?.all_time_ppi || player.playerIndex || 0)
                return indexValue === 0 ? "NR" : indexValue.toFixed(1)
              })()}
            </span>
          </div>
          <Progress
            value={useCustomIndex 
              ? (player.playerIndex || 0)
              : useSeasonIndex
                ? (player.scores?.season_ppi || player.playerIndex || 0)
                : (player.scores?.all_time_ppi || player.playerIndex || 0)
            }
            max={100}
            className="h-2"
            indicatorClassName={getProgressColorClass(
              useCustomIndex 
                ? (player.playerIndex || 0)
                : useSeasonIndex
                  ? (player.scores?.season_ppi || player.playerIndex || 0)
                  : (player.scores?.all_time_ppi || player.playerIndex || 0)
            )}
          />
        </div>

        {/* Advanced Stats Components */}
        <div className="grid grid-cols-4 gap-1 text-center mt-1 bg-muted/30 rounded-sm p-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="cursor-help">
                  <p className="text-xs font-medium">
                    {useSeasonIndex 
                      ? (player.scores?.season_poi?.toFixed(1) || 'N/A')
                      : (player.scores?.all_time_poi?.toFixed(1) || 'N/A')
                    }
                  </p>
                  <p className="text-[10px] text-muted-foreground">POI</p>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">POI is the Player Offensive Index</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="cursor-help">
                  <div className="cursor-help">
                    <p className="text-xs font-medium">
                      {useSeasonIndex 
                        ? (player.scores?.season_pdi?.toFixed(1) || 'N/A')
                        : (player.scores?.all_time_pdi?.toFixed(1) || 'N/A')
                      }
                    </p>
                    <p className="text-[10px] text-muted-foreground">PDI</p>
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">PDI is the Player Defensive Index</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="cursor-help">
                  <p className="text-xs font-medium">{player.advancedStats?.adj_pps?.toFixed(2) || 'N/A'}</p>
                  <p className="text-[10px] text-muted-foreground">Adj PPS</p>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">Adj PPS is the Adjusted Points Per Shot</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="cursor-help">
                  <p className="text-xs font-medium">{player.advancedStats?.usg_pct?.toFixed(1) || 'N/A'}</p>
                  <p className="text-[10px] text-muted-foreground">USG%</p>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">USG% is the Usage Percentage</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <div className="grid grid-cols-3 gap-2 text-center">
          <div>
            <p className="text-2xl font-bold">{player.stats?.ppg?.toFixed(1) || 'N/A'}</p>
            <p className="text-xs text-muted-foreground">PPG</p>
          </div>
          <div>
            <p className="text-2xl font-bold">{player.stats?.apg?.toFixed(1) || 'N/A'}</p>
            <p className="text-xs text-muted-foreground">APG</p>
          </div>
          <div>
            <p className="text-2xl font-bold">{player.stats?.rpg?.toFixed(1) || 'N/A'}</p>
            <p className="text-xs text-muted-foreground">RPG</p>
          </div>
        </div>

        <div className="flex justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => { e.preventDefault(); e.stopPropagation(); onSelect(); }}
            className="focus:outline-none focus-visible:outline-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0"
          >
            <BarChart className="h-4 w-4 mr-2" />
            View Stats
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

