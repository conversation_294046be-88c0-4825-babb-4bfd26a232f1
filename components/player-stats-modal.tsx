"use client"

import { Player } from "@/types/player"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { TableIcon, BarChart3Icon, ShieldIcon } from "lucide-react"

interface PlayerStatsModalProps {
  player: Player | null
  isOpen: boolean
  onClose: () => void
}

export function PlayerStatsModal({ player, isOpen, onClose }: PlayerStatsModalProps) {
  if (!player) return null

  // Function to determine the color class based on the player index value
  const getIndexColorClass = (index: number) => {
    if (index >= 90) return "text-green-600 dark:text-green-400"
    if (index >= 80) return "text-emerald-600 dark:text-emerald-400"
    if (index >= 70) return "text-blue-600 dark:text-blue-400"
    if (index >= 60) return "text-yellow-600 dark:text-yellow-400"
    if (index >= 50) return "text-orange-600 dark:text-orange-400"
    return "text-red-600 dark:text-red-400"
  }

  // Function to determine the progress color based on the player index value
  const getProgressColorClass = (index: number) => {
    if (index >= 90) return "bg-green-600"
    if (index >= 80) return "bg-emerald-600"
    if (index >= 70) return "bg-blue-600"
    if (index >= 60) return "bg-yellow-600"
    if (index >= 50) return "bg-orange-600"
    return "bg-red-600"
  }

  // Format percentage values
  const formatPercentage = (value: number | undefined) => {
    if (value === undefined) return "-"
    return `${value.toFixed(1)}%`
  }

  // Format decimal values
  const formatDecimal = (value: number | undefined) => {
    if (value === undefined) return "-"
    return value.toFixed(1)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl flex items-center gap-2">
            {player.name}
            {player.transferPortal && (
              <Badge variant={player.status === "Committed" ? "destructive" : "default"} className="ml-2">
                {player.status}
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Player details
          </DialogDescription>
          <div className="flex flex-wrap gap-4 text-base mt-2">
            <span>
              <span className="font-semibold">Position:</span> {player.position}
            </span>
            <span>
              <span className="font-semibold">Team:</span> {player.team}
            </span>
            <span>
              <span className="font-semibold">Height:</span> {player.height}
            </span>
            <span>
              <span className="font-semibold">Weight:</span> {player.weight} lbs
            </span>
            <span>
              <span className="font-semibold">Player Index:</span>{" "}
              <span className={`font-bold ${getIndexColorClass(player.playerIndex)}`}>
                {player.playerIndex === 0 ? "NR" : player.playerIndex.toFixed(1)}
              </span>
            </span>
            <span>
              <span className="font-semibold">GP:</span> {player.stats.gp}
            </span>
            <span>
              <span className="font-semibold">MPG:</span> {player.stats.mpg}
            </span>
          </div>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full mt-4">
          <TabsList className="grid grid-cols-3 w-full mb-4">
            <TabsTrigger value="basic">
              <TableIcon className="w-4 h-4 mr-2" />
              Basic Stats
            </TabsTrigger>
            <TabsTrigger value="offensive">
              <BarChart3Icon className="w-4 h-4 mr-2" />
              Offensive
            </TabsTrigger>
            <TabsTrigger value="defensive">
              <ShieldIcon className="w-4 h-4 mr-2" />
              Defensive
            </TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Scoring</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>Points Per Game:</span>
                    <span className="font-semibold">{player.stats.ppg.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>FG:</span>
                    <span className="font-semibold">
                      {player.stats.fgm?.toFixed(1) || "-"}/{player.stats.fga?.toFixed(1) || "-"} ({player.stats.fg_pct ? `${player.stats.fg_pct.toFixed(1)}%` : "-"})
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>3PT:</span>
                    <span className="font-semibold">
                      {player.stats['3pm']?.toFixed(1) || "-"}/{player.stats['3pa']?.toFixed(1) || "-"} ({player.stats['3p_pct'] ? `${player.stats['3p_pct'].toFixed(1)}%` : "-"})
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>FT:</span>
                    <span className="font-semibold">
                      {player.stats.ftm?.toFixed(1) || "-"}/{player.stats.fta?.toFixed(1) || "-"} ({player.stats.ft_pct ? `${player.stats.ft_pct.toFixed(1)}%` : "-"})
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Rebounds</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>Total Rebounds:</span>
                    <span className="font-semibold">{player.stats.rpg.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Offensive Rebounds:</span>
                    <span className="font-semibold">{player.stats.orb?.toFixed(1) || "-"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Defensive Rebounds:</span>
                    <span className="font-semibold">{player.stats.drb?.toFixed(1) || "-"}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Other Stats</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>Assists:</span>
                    <span className="font-semibold">{player.stats.apg.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Steals:</span>
                    <span className="font-semibold">{player.stats.spg.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Blocks:</span>
                    <span className="font-semibold">{player.stats.bpg.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Turnovers:</span>
                    <span className="font-semibold">{player.stats.tov?.toFixed(1) || "-"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Personal Fouls:</span>
                    <span className="font-semibold">{player.stats.pf?.toFixed(1) || "-"}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="offensive" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Shooting Efficiency</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>True Shooting %:</span>
                    <span className="font-semibold">{formatPercentage(player.advancedStats?.ts_pct)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Effective FG %:</span>
                    <span className="font-semibold">{formatPercentage(player.advancedStats?.efg_pct)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Shooting %:</span>
                    <span className="font-semibold">{formatPercentage(player.advancedStats?.total_s_pct)}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Offensive Contribution</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>Offensive Rebound %:</span>
                    <span className="font-semibold">{formatPercentage(player.advancedStats?.orb)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Assist %:</span>
                    <span className="font-semibold">{formatPercentage(player.advancedStats?.ast)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Turnover %:</span>
                    <span className="font-semibold">{formatPercentage(player.advancedStats?.tov)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Usage Rate:</span>
                    <span className="font-semibold">{formatPercentage(player.advancedStats?.usg)}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Offensive Metrics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>Points Per Shot:</span>
                    <span className="font-semibold">{formatDecimal(player.advancedStats?.pps)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Pure Point Rating:</span>
                    <span className="font-semibold">{formatDecimal(player.advancedStats?.ppr)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Offensive Rating:</span>
                    <span className="font-semibold">{formatDecimal(player.advancedStats?.ortg)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Free Throw Rate:</span>
                    <span className="font-semibold">{formatDecimal(player.advancedStats?.ftrate)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="defensive" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Rebounding</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>Defensive Rebound %:</span>
                    <span className="font-semibold">{formatPercentage(player.advancedStats?.drb)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Rebound %:</span>
                    <span className="font-semibold">{formatPercentage(player.advancedStats?.trb)}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Defensive Impact</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>Steal %:</span>
                    <span className="font-semibold">{formatPercentage(player.advancedStats?.stl)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Block %:</span>
                    <span className="font-semibold">{formatPercentage(player.advancedStats?.blk)}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Defensive Metrics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>Defensive Rating:</span>
                    <span className="font-semibold">{formatDecimal(player.advancedStats?.drtg)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Efficiency Differential:</span>
                    <span className="font-semibold">{formatDecimal(player.advancedStats?.ediff)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-4">
          <h3 className="text-lg font-semibold mb-2">PlayVison Player Index</h3>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Progress
                value={player.playerIndex}
                max={100}
                className="h-3"
                indicatorClassName={getProgressColorClass(player.playerIndex)}
              />
            </div>
            <div className={`text-xl font-bold ${getIndexColorClass(player.playerIndex)}`}>
              {player.playerIndex === 0 ? "NR" : player.playerIndex.toFixed(1)}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
