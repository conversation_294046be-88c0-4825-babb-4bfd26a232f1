"use client"

import { useState } from "react"
import { Player } from "@/types/player"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Search, X, Plus } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

interface PlayerSelectionPanelProps {
  allPlayers: Player[]
  selectedPlayers: Player[]
  onAddPlayer: (player: Player) => void
  onRemovePlayer: (playerId: string | number) => void
}

export function PlayerSelectionPanel({
  allPlayers,
  selectedPlayers,
  onAddPlayer,
  onRemovePlayer
}: PlayerSelectionPanelProps) {
  const [searchTerm, setSearchTerm] = useState("")

  // Filter players based on search term
  const filteredPlayers = searchTerm
    ? allPlayers.filter(player =>
        player.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !selectedPlayers.some(p => p.id === player.id)
      )
    : []

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Selected Players ({selectedPlayers.length}/4)</CardTitle>
          <p className="text-sm text-muted-foreground">Players selected for comparison</p>
        </CardHeader>
        <CardContent>
          {selectedPlayers.length === 0 ? (
            <p className="text-muted-foreground text-sm">No players selected yet. Search and add players below.</p>
          ) : (
            <div className="space-y-2">
              {selectedPlayers.map(player => (
                <div
                  key={player.id}
                  className="flex items-center justify-between p-2 rounded-md bg-muted"
                >
                  <div>
                    <div className="font-medium">{player.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {player.team} · {player.position}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemovePlayer(player.id)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                    <span className="sr-only">Remove</span>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Add Players</CardTitle>
          <p className="text-sm text-muted-foreground">Search for players to add to comparison</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search players..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {searchTerm && (
              <ScrollArea className="h-[300px]">
                {filteredPlayers.length > 0 ? (
                  <div className="space-y-2">
                    {filteredPlayers.slice(0, 10).map(player => (
                      <div
                        key={player.id}
                        className="flex items-center justify-between p-2 rounded-md hover:bg-muted"
                      >
                        <div>
                          <div className="font-medium">{player.name}</div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>{player.team}</span>
                            <Badge variant="outline" className="text-xs h-5">
                              {player.position}
                            </Badge>
                            {player.transferPortal && (
                              <Badge
                                variant={player.status === 'Committed' ? "destructive" : "outline"}
                                className={`text-xs h-5 ${player.status === 'Committed' ? "" : "bg-green-600 hover:bg-green-700 text-white"}`}
                              >
                                {player.status}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onAddPlayer(player)}
                          disabled={selectedPlayers.length >= 4}
                          className="h-8 w-8 p-0"
                        >
                          <Plus className="h-4 w-4" />
                          <span className="sr-only">Add</span>
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground text-sm py-2">No players found matching "{searchTerm}"</p>
                )}
              </ScrollArea>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
