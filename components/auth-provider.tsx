"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from "react"
import { useRouter, usePathname } from "next/navigation"
import { supabase } from "@/lib/supabase"
import { Session, User } from "@supabase/supabase-js"

interface AuthContextType {
  isAuthenticated: boolean
  user: User | null
  session: Session | null
  login: (email: string, password: string) => Promise<{ error: any | null }>
  signup: (email: string, password: string, name: string) => Promise<{ error: any | null, needsEmailVerification: boolean }>
  logout: () => Promise<void>
  checkEmailAllowed: (email: string) => Promise<{ allowed: boolean; message?: string }>
}

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  user: null,
  session: null,
  login: async () => ({ error: null }),
  signup: async () => ({ error: null, needsEmailVerification: false }),
  logout: async () => {},
  checkEmailAllowed: async () => ({ allowed: false }),
})

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  // Check authentication status on mount and set up auth listener
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        setIsAuthenticated(!!session)
      }
    )

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      setIsAuthenticated(!!session)
      setIsLoading(false)
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  // Handle redirects based on auth status
  useEffect(() => {
    if (isLoading) return

    // Redirect to login if not authenticated and trying to access protected routes
    if (!isAuthenticated && pathname === "/") {
      router.push("/login")
    }

    // Redirect to home if authenticated and trying to access auth pages
    if (isAuthenticated && (pathname === "/login" || pathname === "/register")) {
      router.push("/")
    }
  }, [isAuthenticated, pathname, router, isLoading])

  // Check if email is allowed to register
  const checkEmailAllowed = async (email: string): Promise<{ allowed: boolean; message?: string }> => {
    try {
      const response = await fetch('/api/auth/check-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (!data.allowed) {
        return {
          allowed: false,
          message: data.message || 'This email is not authorized for registration.'
        }
      }

      return { allowed: true }
    } catch (error) {
      console.error('Error checking email:', error)
      return {
        allowed: false,
        message: 'Error verifying email. Please try again.'
      }
    }
  }

  // Login with email and password
  const login = async (email: string, password: string) => {
    try {
      const normalizedEmail = email.toLowerCase().trim()
      const { data, error } = await supabase.auth.signInWithPassword({
        email: normalizedEmail,
        password,
      })

      if (error) throw error
      return { error: null }
    } catch (error) {
      console.error('Error logging in:', error)
      return { error }
    }
  }

  // Sign up with email and password
  const signup = async (email: string, password: string, name: string) => {
    try {
      const normalizedEmail = email.toLowerCase().trim()
      
      // First check if email is allowed
      const { allowed, message } = await checkEmailAllowed(normalizedEmail)
      if (!allowed) {
        return {
          error: { message: message || 'This email is not authorized for registration' },
          needsEmailVerification: false
        }
      }

      // If email is allowed, proceed with signup
      const { data, error } = await supabase.auth.signUp({
        email: normalizedEmail,
        password,
        options: {
          data: {
            full_name: name,
          },
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) {
        // Check for duplicate email error
        if (error.message?.includes('already registered')) {
          return {
            error: { message: 'An account with this email already exists. Please log in instead.' },
            needsEmailVerification: false
          }
        }
        throw error
      }

      // Check if email confirmation is required
      const needsEmailVerification = data?.user?.identities?.length === 0 ||
                                    data?.user?.confirmed_at === null

      return { error: null, needsEmailVerification }
    } catch (error) {
      console.error('Error signing up:', error)
      return { error, needsEmailVerification: false }
    }
  }

  // Logout
  const logout = async () => {
    await supabase.auth.signOut()
    router.push("/login")
  }

  return (
    <AuthContext.Provider value={{
      isAuthenticated,
      user,
      session,
      login,
      signup,
      logout,
      checkEmailAllowed
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => useContext(AuthContext)
