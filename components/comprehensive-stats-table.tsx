'use client'

import { useState, useMemo } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ChevronUp, ChevronDown } from 'lucide-react'

// Stat definitions for tooltips
const statDefinitions: Record<string, string> = {
  // Basic Info
  player_name: "Player's full name",
  position: "Player's position (PG, SG, SF, PF, C)",
  team_name: "Player's college team",
  class: "Player's academic year (Fr, So, Jr, Sr)",
  number: "Player's jersey number",
  height: "Player's height",
  weight: "Player's weight in pounds",
  age: "Player's age",
  conference_name: "Athletic conference the team belongs to",
  role: "Player's role on the team",
  nba_team: "NBA team that drafted the player (if applicable)",
  
  // Basic Stats
  gp: "Games Played",
  mpg: "Minutes Per Game",
  ppg: "Points Per Game",
  rpg: "Rebounds Per Game",
  apg: "Assists Per Game",
  fgm: "Field Goals Made per game",
  fga: "Field Goals Attempted per game",
  fg_pct: "Field Goal Percentage",
  "2pm": "2-Point Field Goals Made",
  "2pa": "2-Point Field Goals Attempted",
  "2p_pct": "2-Point Field Goal Percentage",
  "3pm": "Three-Point Field Goals Made per game",
  "3pa": "Three-Point Attempts per game",
  "3p_pct": "Three-Point Percentage",
  ftm: "Free Throws Made per game",
  fta: "Free Throws Attempted per game",
  ft_pct: "Free Throw Percentage",
  orb: "Offensive Rebounds per game",
  drb: "Defensive Rebounds per game",
  spg: "Steals per game",
  bpg: "Blocks per game",
  tov: "Turnovers per game",
  pf: "Personal Fouls per game",
  ppm: "Points Per Minute",
  apm: "Assists Per Minute",
  rpm: "Rebounds Per Minute",
  
  // Shooting Stats
  rim_makes: "Field Goals Made at the Rim",
  rim_attempts: "Field Goals Attempted at the Rim",
  rim_pct: "Rim Field Goal Percentage",
  mid_range_makes: "Mid-Range Field Goals Made",
  mid_range_attempts: "Mid-Range Field Goals Attempted",
  mid_range_pct: "Mid-Range Field Goal Percentage",
  
  // Advanced Stats
  ftr: "Free Throw Rate - ratio of FTA to FGA",
  ts_pct: "True Shooting Percentage - accounts for FG, 3P, and FT efficiency",
  efg_pct: "Effective Field Goal Percentage - adjusts FG% to give more weight to 3-pointers",
  total_s_pct: "Total Shooting Percentage - scoring efficiency weighted by usage",
  min_pct: "Percentage of team minutes the player was on court",
  orb_pct: "Offensive Rebound Rate - estimate of available offensive rebounds grabbed",
  drb_pct: "Defensive Rebound Rate",
  trb_pct: "Total Rebound Rate",
  ast_pct: "Assist Rate - percentage of teammate field goals assisted",
  tov_pct: "Turnover Rate - percentage of possessions ending in a turnover",
  stl_pct: "Steal Rate - percentage of opponent possessions ending in a steal",
  blk_pct: "Block Rate",
  usg_pct: "Usage Rate - percentage of team plays used by the player",
  ppr: "100 x (League Pace / Team Pace) x ([(Assists x 2/3) - Turnovers] / Minutes)",
  pps: "Points Per Shot",
  ast_tov: "Assist-to-Turnover Ratio",
  personal_foul_rate: "Fouls committed per minute or per 100 possessions",
  "3p_100": "3-Point Attempts per 100 possessions",
  ortg: "Offensive Rating - points produced per 100 possessions",
  drtg: "Defensive Rating - points allowed per 100 possessions",
  ediff: "Efficiency Differential - ORtg minus DRtg",
  porpag: "Points Over Replacement Per Adjusted Game",
  adj_off_efficiency: "Adjusted Offensive Efficiency - points per 100 possessions, adjusted for strength of schedule",
  adj_def_efficiency: "Adjusted Defensive Rating - defensive points per 100 possessions, adjusted for strength of schedule",
  dporpag: "Defensive Points Over Replacement Per Adjusted Game",
  stops: "Estimated defensive stops contributed",
  fic: "Floor Impact Counter - overall productivity metric",
  per: "Player Efficiency Rating - per-minute rating of performance",
  rec_rank: "Recruiting ranking",
  bpm: "Box Plus-Minus",
  obpm: "Offensive Box Plus-Minus",
  dbpm: "Defensive Box Plus-Minus",
  gbpm: "Generic Box Plus-Minus",
  ogbpm: "Offensive GBPM",
  dgbpm: "Defensive GBPM",
  season: "Basketball season (e.g., 2023_24)"
}

interface Player {
  player_id: number
  player_name?: string
  name?: string
  position?: string
  team_name?: string
  team?: string
  class?: string
  number?: string
  height?: string
  weight?: number
  age?: number
  conference_name?: string
  conference?: string
  role?: string
  nba_team?: string
  season?: string
  
  // Basic stats - can be nested in stats object or flat
  gp?: number
  mpg?: number
  ppg?: number
  rpg?: number
  apg?: number
  fgm?: number
  fga?: number
  fg_pct?: number
  '2pm'?: number
  '2pa'?: number
  '2p_pct'?: number
  '3pm'?: number
  '3pa'?: number
  '3p_pct'?: number
  ftm?: number
  fta?: number
  ft_pct?: number
  orb?: number
  drb?: number
  spg?: number
  bpg?: number
  tov?: number
  pf?: number
  ppm?: number
  apm?: number
  rpm?: number
  
  // Advanced stats - can be nested in advancedStats object or flat
  rim_makes?: number
  rim_attempts?: number
  rim_pct?: number
  mid_range_makes?: number
  mid_range_attempts?: number
  mid_range_pct?: number
  dunks_makes?: number
  dunks_attempts?: number
  dunks_pct?: number
  ftr?: number
  ts_pct?: number
  efg_pct?: number
  total_s_pct?: number
  min_pct?: number
  orb_pct?: number
  drb_pct?: number
  trb_pct?: number
  ast_pct?: number
  tov_pct?: number
  stl_pct?: number
  blk_pct?: number
  usg_pct?: number
  ppr?: number
  pps?: number
  ast_tov?: number
  personal_foul_rate?: number
  '3p_100'?: number
  ortg?: number
  drtg?: number
  ediff?: number
  porpag?: number
  adj_off_efficiency?: number
  adj_def_efficiency?: number
  dporpag?: number
  stops?: number
  fic?: number
  per?: number
  rec_rank?: number
  bpm?: number
  obpm?: number
  dbpm?: number
  gbpm?: number
  ogbpm?: number
  dgbpm?: number
  
  // Nested objects for compatibility
  stats?: any
  advancedStats?: any
}

interface ComprehensiveStatsTableProps {
  players: Player[]
  onPlayerSelect?: (player: Player) => void
  selectedPlayerId?: string | number
}

type SortField = keyof Player | string
type SortDirection = 'asc' | 'desc'

export function ComprehensiveStatsTable({ players, onPlayerSelect, selectedPlayerId }: ComprehensiveStatsTableProps) {
  const [sortField, setSortField] = useState<SortField>('ppg')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 25

  // Helper function to get nested values
  const getValue = (player: Player, field: string): any => {
    // Try direct access first
    if (player[field as keyof Player] !== undefined) {
      return player[field as keyof Player]
    }
    // Try stats object
    if (player.stats?.[field] !== undefined) {
      return player.stats[field]
    }
    // Try advancedStats object
    if (player.advancedStats?.[field] !== undefined) {
      return player.advancedStats[field]
    }
    return null
  }

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
    setCurrentPage(1)
  }

  const SortButton = ({ field, children }: { field: SortField; children: React.ReactNode }) => (
    <Button
      variant="ghost"
      size="sm"
      className="h-auto p-0 font-semibold hover:bg-transparent"
      onClick={() => handleSort(field)}
    >
      {children}
      {sortField === field && (
        sortDirection === 'asc' ? <ChevronUp className="ml-1 h-3 w-3" /> : <ChevronDown className="ml-1 h-3 w-3" />
      )}
    </Button>
  )

  // Helper component for sortable headers with tooltips
  const SortableHeaderWithTooltip = ({ field, children }: { field: string; children: React.ReactNode }) => {
    const definition = statDefinitions[field] || "No definition available"
    
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div>
              <SortButton field={field}>{children}</SortButton>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p className="max-w-xs text-sm">{definition}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  // Filter players
  const filteredPlayers = useMemo(() => {
    return players
  }, [players])

  // Sort players
  const sortedPlayers = useMemo(() => {
    return [...filteredPlayers].sort((a, b) => {
      const aValue = getValue(a, sortField as string)
      const bValue = getValue(b, sortField as string)
      
      // Handle null/undefined values
      if (aValue === null || aValue === undefined) return 1
      if (bValue === null || bValue === undefined) return -1
      
      // Convert to numbers if possible
      const aNum = typeof aValue === 'number' ? aValue : parseFloat(aValue)
      const bNum = typeof bValue === 'number' ? bValue : parseFloat(bValue)
      
      if (!isNaN(aNum) && !isNaN(bNum)) {
        return sortDirection === 'asc' ? aNum - bNum : bNum - aNum
      }
      
      // String comparison
      const aStr = String(aValue).toLowerCase()
      const bStr = String(bValue).toLowerCase()
      return sortDirection === 'asc' ? aStr.localeCompare(bStr) : bStr.localeCompare(aStr)
    })
  }, [filteredPlayers, sortField, sortDirection])

  // Paginate players
  const totalPages = Math.ceil(sortedPlayers.length / itemsPerPage)
  const paginatedPlayers = sortedPlayers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  // Format functions
  const formatStat = (value: any) => {
    if (value === null || value === undefined) return 'N/A'
    if (typeof value === 'number') {
      return value.toFixed(1)
    }
    return value.toString()
  }

  const formatPercentage = (value: any) => {
    if (value === null || value === undefined) return 'N/A'
    if (typeof value === 'number') {
      return `${(value * 100).toFixed(1)}%`
    }
    return 'N/A'
  }

  const formatAlreadyPercentage = (value: any) => {
    if (value === null || value === undefined) return 'N/A'
    if (typeof value === 'number') {
      return `${value.toFixed(1)}%`
    }
    return 'N/A'
  }

  return (
    <div className="space-y-4">


      <div className="rounded-md border overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              {/* Basic Info */}
              <TableHead className="sticky left-0 bg-background z-10"><SortableHeaderWithTooltip field="player_name">Name</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="position">Pos</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="team_name">Team</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="class">Class</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="number">No.</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="height">Height</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="weight">Weight</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="age">Age</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="conference_name">Conference</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="role">Role</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="nba_team">NBA Team</SortableHeaderWithTooltip></TableHead>
              
              {/* Basic Stats */}
              <TableHead><SortableHeaderWithTooltip field="gp">GP</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="mpg">MPG</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="ppg">PPG</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="rpg">RPG</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="apg">APG</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="fgm">FGM</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="fga">FGA</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="fg_pct">FG%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="2pm">2PM</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="2pa">2PA</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="2p_pct">2P%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="3pm">3PM</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="3pa">3PA</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="3p_pct">3P%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="ftm">FTM</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="fta">FTA</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="ft_pct">FT%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="orb">ORB</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="drb">DRB</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="spg">SPG</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="bpg">BPG</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="tov">TOV</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="pf">PF</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="ppm">PPM</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="apm">APM</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="rpm">RPM</SortableHeaderWithTooltip></TableHead>
              
              {/* Shooting Stats */}
              <TableHead><SortableHeaderWithTooltip field="rim_makes">Rim Makes</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="rim_attempts">Rim Att</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="rim_pct">Rim%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="mid_range_makes">Mid Makes</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="mid_range_attempts">Mid Att</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="mid_range_pct">Mid%</SortableHeaderWithTooltip></TableHead>
              
              {/* Advanced Stats */}
              <TableHead><SortableHeaderWithTooltip field="ftr">FTR</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="ts_pct">TS%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="efg_pct">eFG%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="total_s_pct">Total S%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="min_pct">MIN%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="orb_pct">ORB%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="drb_pct">DRB%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="trb_pct">TRB%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="ast_pct">AST%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="tov_pct">TOV%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="stl_pct">STL%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="blk_pct">BLK%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="usg_pct">USG%</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="ppr">PPR</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="pps">PPS</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="ast_tov">AST/TOV</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="personal_foul_rate">PF Rate</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="3p_100">3P/100</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="ortg">ORtg</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="drtg">DRtg</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="ediff">eDiff</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="porpag">PORPAG</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="adj_off_efficiency">Adj OE</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="adj_def_efficiency">Adj DE</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="dporpag">DPORPAG</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="stops">Stops</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="fic">FIC</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="per">PER</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="rec_rank">Rec Rank</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="bpm">BPM</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="obpm">OBPM</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="dbpm">DBPM</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="gbpm">GBPM</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="ogbpm">OGBPM</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="dgbpm">DGBPM</SortableHeaderWithTooltip></TableHead>
              <TableHead><SortableHeaderWithTooltip field="season">Season</SortableHeaderWithTooltip></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedPlayers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={74} className="text-center text-muted-foreground">
                  No players found
                </TableCell>
              </TableRow>
            ) : (
              paginatedPlayers.map((player, index) => (
                <TableRow
                  key={player.player_id || index}
                  className={`cursor-pointer hover:bg-muted/50 ${
                    selectedPlayerId === player.player_id ? "bg-muted" : ""
                  }`}
                  onClick={() => onPlayerSelect?.(player)}
                >
                  {/* Basic Info */}
                  <TableCell className="sticky left-0 bg-background font-medium whitespace-nowrap">
                    {player.player_name || player.name || 'N/A'}
                  </TableCell>
                  <TableCell>{player.position || 'N/A'}</TableCell>
                  <TableCell className="whitespace-nowrap">{player.team_name || player.team || 'N/A'}</TableCell>
                  <TableCell>{player.class || 'N/A'}</TableCell>
                  <TableCell>{player.number || 'N/A'}</TableCell>
                  <TableCell>{player.height || 'N/A'}</TableCell>
                  <TableCell>{formatStat(player.weight)}</TableCell>
                  <TableCell>{formatStat(player.age)}</TableCell>
                  <TableCell className="whitespace-nowrap">{player.conference_name || player.conference || 'N/A'}</TableCell>
                  <TableCell>{player.role || 'N/A'}</TableCell>
                  <TableCell>{player.nba_team || 'N/A'}</TableCell>
                  
                  {/* Basic Stats */}
                  <TableCell>{formatStat(getValue(player, 'gp'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'mpg'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'ppg'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'rpg'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'apg'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'fgm'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'fga'))}</TableCell>
                  <TableCell>{formatPercentage(getValue(player, 'fg_pct'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, '2pm'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, '2pa'))}</TableCell>
                  <TableCell>{formatPercentage(getValue(player, '2p_pct'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, '3pm'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, '3pa'))}</TableCell>
                  <TableCell>{formatPercentage(getValue(player, '3p_pct'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'ftm'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'fta'))}</TableCell>
                  <TableCell>{formatPercentage(getValue(player, 'ft_pct'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'orb'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'drb'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'spg'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'bpg'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'tov'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'pf'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'ppm'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'apm'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'rpm'))}</TableCell>
                  
                  {/* Shooting Stats */}
                  <TableCell>{formatStat(getValue(player, 'rim_makes'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'rim_attempts'))}</TableCell>
                  <TableCell>{formatPercentage(getValue(player, 'rim_pct'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'mid_range_makes'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'mid_range_attempts'))}</TableCell>
                  <TableCell>{formatPercentage(getValue(player, 'mid_range_pct'))}</TableCell>
                  
                  {/* Advanced Stats */}
                  <TableCell>{formatAlreadyPercentage(getValue(player, 'ftr'))}</TableCell>
                  <TableCell>{formatPercentage(getValue(player, 'ts_pct'))}</TableCell>
                  <TableCell>{formatPercentage(getValue(player, 'efg_pct'))}</TableCell>
                  <TableCell>{formatAlreadyPercentage(getValue(player, 'total_s_pct'))}</TableCell>
                  <TableCell>{formatAlreadyPercentage(getValue(player, 'min_pct'))}</TableCell>
                  <TableCell>{formatAlreadyPercentage(getValue(player, 'orb_pct'))}</TableCell>
                  <TableCell>{formatAlreadyPercentage(getValue(player, 'drb_pct'))}</TableCell>
                  <TableCell>{formatAlreadyPercentage(getValue(player, 'trb_pct'))}</TableCell>
                  <TableCell>{formatAlreadyPercentage(getValue(player, 'ast_pct'))}</TableCell>
                  <TableCell>{formatAlreadyPercentage(getValue(player, 'tov_pct'))}</TableCell>
                  <TableCell>{formatAlreadyPercentage(getValue(player, 'stl_pct'))}</TableCell>
                  <TableCell>{formatAlreadyPercentage(getValue(player, 'blk_pct'))}</TableCell>
                  <TableCell>{formatAlreadyPercentage(getValue(player, 'usg_pct'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'ppr'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'pps'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'ast_tov'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'personal_foul_rate'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, '3p_100'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'ortg'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'drtg'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'ediff'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'porpag'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'adj_off_efficiency'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'adj_def_efficiency'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'dporpag'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'stops'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'fic'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'per'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'rec_rank'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'bpm'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'obpm'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'dbpm'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'gbpm'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'ogbpm'))}</TableCell>
                  <TableCell>{formatStat(getValue(player, 'dgbpm'))}</TableCell>
                  <TableCell>{player.season || 'N/A'}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, sortedPlayers.length)} of {sortedPlayers.length} players
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="text-sm">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
} 