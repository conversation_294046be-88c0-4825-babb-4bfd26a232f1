"use client"

import { SetStateAction, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ArrowDown, ArrowUp, ArrowUpDown, ChevronLeft, ChevronRight, Search, ExternalLink } from "lucide-react"
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area"

export function StatsTable({
  players,
  selectedPlayerId,
  onPlayerSelect
}: {
  players: Array<any>; // Accept any player data structure
  selectedPlayerId: string | number | null;
  onPlayerSelect: (player: any) => void;
}) {
  const [sortField, setSortField] = useState("name") // Default sort by player name
  const [sortDirection, setSortDirection] = useState("asc") // Default sort direction ascending
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  // Debug: Log the first few players to see the data structure
  console.log('StatsTable received players:', players.slice(0, 3))

  const handleSort = (field: SetStateAction<string>) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("desc") // Default to descending for new sort fields
    }
  }

  const getSortIcon = (field: string) => {
    if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />
    return sortDirection === "asc" ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />
  }

  // Filter players based on search term
  const filteredPlayers = players.filter((player) => 
    (player.name || player.player_name || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Sort players
  const sortedPlayers = [...filteredPlayers].sort((a, b) => {
    let aValue: any, bValue: any;

    // Helper function to get nested values
    const getValue = (obj: any, path: string) => {
      return path.split('.').reduce((o, p) => o?.[p], obj) || 0;
    }

    aValue = getValue(a, sortField);
    bValue = getValue(b, sortField);

    if (typeof aValue === "string") {
      return sortDirection === "asc" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    }

    return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
  })

  // Pagination
  const totalPages = Math.ceil(sortedPlayers.length / itemsPerPage)
  const paginatedPlayers = sortedPlayers.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  // Helper function to format numbers
  const formatStat = (value: any, isPercentage = false) => {
    console.log('formatStat called with:', value, 'isPercentage:', isPercentage)
    if (value === null || value === undefined || value === '') return '-'
    const num = parseFloat(value)
    if (isNaN(num)) return '-'
    if (isPercentage) {
      const result = `${(num * 100).toFixed(1)}%`
      console.log('Percentage result:', result)
      return result
    }
    const result = num.toFixed(1)
    console.log('Number result:', result)
    return result
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search players..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value)
              setCurrentPage(1)
            }}
          />
        </div>
      </div>

      <ScrollArea className="w-full">
        <div className="rounded-md border border-border">
          <table className="min-w-max w-full border-collapse">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2 font-semibold sticky left-0 bg-background">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("name")}
                    className="flex items-center gap-1 p-0 h-auto font-medium"
                  >
                    Player {getSortIcon("name")}
                  </Button>
                </th>
                <th className="p-2 text-left">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("position")}
                    className="flex items-center gap-1 p-0 h-auto font-medium"
                  >
                    Pos {getSortIcon("position")}
                  </Button>
                </th>
                <th className="p-2 text-left">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("class")}
                    className="flex items-center gap-1 p-0 h-auto font-medium"
                  >
                    Class {getSortIcon("class")}
                  </Button>
                </th>
                <th className="p-2 text-left">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("team")}
                    className="flex items-center gap-1 p-0 h-auto font-medium"
                  >
                    Team {getSortIcon("team")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.gp")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    GP {getSortIcon("stats.gp")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.mpg")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    MPG {getSortIcon("stats.mpg")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.ppg")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    PPG {getSortIcon("stats.ppg")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.fgm")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    FGM {getSortIcon("stats.fgm")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.fga")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    FGA {getSortIcon("stats.fga")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.fg_pct")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    FG% {getSortIcon("stats.fg_pct")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.3pm")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    3PM {getSortIcon("stats.3pm")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.3pa")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    3PA {getSortIcon("stats.3pa")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.3p_pct")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    3P% {getSortIcon("stats.3p_pct")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.ftm")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    FTM {getSortIcon("stats.ftm")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.fta")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    FTA {getSortIcon("stats.fta")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.ft_pct")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    FT% {getSortIcon("stats.ft_pct")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.orb")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    ORB {getSortIcon("stats.orb")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.drb")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    DRB {getSortIcon("stats.drb")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.rpg")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    RPG {getSortIcon("stats.rpg")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.apg")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    APG {getSortIcon("stats.apg")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.spg")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    SPG {getSortIcon("stats.spg")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.bpg")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    BPG {getSortIcon("stats.bpg")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.tov")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    TOV {getSortIcon("stats.tov")}
                  </Button>
                </th>
                <th className="p-2 text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("stats.pf")}
                    className="flex items-center gap-1 p-0 h-auto font-medium ml-auto"
                  >
                    PF {getSortIcon("stats.pf")}
                  </Button>
                </th>
              </tr>
            </thead>
            <tbody>
              {paginatedPlayers.length > 0 ? (
                paginatedPlayers.map((player, index) => {
                  // Debug: Log the first player to see structure
                  if (index === 0) {
                    console.log('First player in table:', player)
                    console.log('First player stats:', player.stats)
                  }
                  
                  return (
                  <tr
                    key={player.id || player.player_id}
                    className={`border-b cursor-pointer ${selectedPlayerId === (player.id || player.player_id) ? "bg-muted" : ""}`}
                    onClick={() => onPlayerSelect(player)}
                  >
                    <td className="p-2 font-medium sticky left-0 bg-background">
                      <div className="flex items-center gap-2">
                        <span className="text-primary hover:underline flex items-center gap-1 cursor-pointer">
                          {player.name || player.player_name}
                          <ExternalLink className="h-3 w-3 opacity-70" />
                        </span>
                      </div>
                    </td>
                    <td className="p-2">{player.position || '-'}</td>
                    <td className="p-2">
                      {player.class ? (
                        <Badge variant="outline" className="text-xs">
                          {player.class}
                        </Badge>
                      ) : '-'}
                    </td>
                    <td className="p-2">{player.team || player.team_name || '-'}</td>
                    <td className="p-2 text-right">{player.stats?.gp || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.mpg || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.ppg || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.fgm || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.fga || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.fg_pct || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.['3pm'] || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.['3pa'] || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.['3p_pct'] || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.ftm || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.fta || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.ft_pct || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.orb || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.drb || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.rpg || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.apg || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.spg || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.bpg || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.tov || 'N/A'}</td>
                    <td className="p-2 text-right">{player.stats?.pf || 'N/A'}</td>
                  </tr>
                  )
                })
              ) : (
                <tr>
                  <td colSpan={24} className="h-24 text-center p-2">
                    No players found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {(currentPage - 1) * itemsPerPage + 1}-
            {Math.min(currentPage * itemsPerPage, filteredPlayers.length)} of {filteredPlayers.length}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Previous Page</span>
            </Button>
            <div className="text-sm">
              Page {currentPage} of {totalPages}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Next Page</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

