"use client"

import { useState, useRef, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { ChevronDown, X } from "lucide-react"

const POSITIONS = ["G", "F", "C"]

interface PositionSelectProps {
  value: string[]
  onValueChange: (value: string[]) => void
}

export function PositionSelect({ value, onValueChange }: PositionSelectProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const toggle = (pos: string) => {
    const next = value.includes(pos) ? value.filter(p => p !== pos) : [...value, pos]
    onValueChange(next)
  }

  const clearAll = () => onValueChange([])
  const selectAll = () => onValueChange([...POSITIONS])

  return (
    <div className="space-y-2 relative" ref={dropdownRef}>
      <Button
        variant="outline"
        className="w-full justify-between text-left font-normal"
        onClick={() => setIsOpen(o => !o)}
      >
        <span className="truncate">
          {value.length === 0 ? "Select positions..." : `${value.length} selected`}
        </span>
        <ChevronDown className="h-4 w-4 opacity-50" />
      </Button>

      {isOpen && (
        <div className="absolute z-50 mt-1 w-full rounded-md border bg-popover shadow-md">
          <div className="border-b p-2 flex justify-between">
            <Button variant="ghost" size="sm" onClick={selectAll}>Select All</Button>
            <Button variant="ghost" size="sm" onClick={clearAll}>Clear All</Button>
          </div>
          <div className="p-2 space-y-2">
            {POSITIONS.map(pos => (
              <label key={pos} className="flex items-center gap-2 text-sm cursor-pointer">
                <Checkbox checked={value.includes(pos)} onCheckedChange={() => toggle(pos)} />
                <span>{pos}</span>
              </label>
            ))}
          </div>
        </div>
      )}

      {value.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {value.map(pos => (
            <Badge key={pos} variant="secondary" className="text-xs">
              {pos}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={() => toggle(pos)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
} 