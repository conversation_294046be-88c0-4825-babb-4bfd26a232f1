"use client"

import { useState, useEffect, useRef } from "react"
import { Input } from "@/components/ui/input"
import { Search, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { getPlayerData } from "@/utils/data-transform"
// Using native overflow for scroll behavior
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"

interface ConferenceSelectProps {
  value: string[]
  onValueChange: (value: string[]) => void
}

export function ConferenceSelect({ value, onValueChange }: ConferenceSelectProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [conferences, setConferences] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)
  const areaRef = useRef<HTMLDivElement>(null)
  const [isScrolling, setIsScrolling] = useState(false)

  // Load conferences on component mount
  useEffect(() => {
    const loadConferences = async () => {
      try {
        setIsLoading(true)
        const response = await getPlayerData()

        // Extract unique conferences and sort alphabetically
        const uniqueConferences = Array.from(new Set(
          response.players
            .map(player => (player as any).conference)
            .filter(Boolean)
        )).sort()

        setConferences(uniqueConferences)
      } catch (error) {
        console.error('Error loading conferences:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadConferences()
  }, [])

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const filteredConferences = conferences.filter(conference =>
    conference.toLowerCase().includes(searchTerm.toLowerCase())
  ).slice(0, 20)

  const toggleConference = (conference: string) => {
    const isSelected = value.includes(conference)
    const newValue = isSelected ? value.filter(c => c !== conference) : [...value, conference]
    onValueChange(newValue)
  }

  const handleClearAll = () => {
    onValueChange([])
    setSearchTerm("")
    inputRef.current?.focus()
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      if (filteredConferences.length === 1) {
        e.preventDefault()
        toggleConference(filteredConferences[0])
        setShowSuggestions(false)
      }
    } else if (e.key === 'Escape') {
      setShowSuggestions(false)
    }
  }

  useEffect(() => {
    const root = areaRef.current
    if (!root) return
    const viewport = root.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement | null
    if (!viewport) return
    let to: any
    const onScroll = () => {
      setIsScrolling(true)
      clearTimeout(to)
      to = setTimeout(() => setIsScrolling(false), 300)
    }
    viewport.addEventListener('scroll', onScroll, { passive: true })
    return () => {
      viewport.removeEventListener('scroll', onScroll)
      clearTimeout(to)
    }
  }, [])

  return (
    <div className="relative">
      <div className="relative">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={isLoading ? "Loading conferences..." : "Search conferences..."}
          value={searchTerm}
          onChange={e => { setSearchTerm(e.target.value); setShowSuggestions(true) }}
          onFocus={() => setShowSuggestions(true)}
          onKeyDown={handleKeyDown}
          className="pl-8 pr-8"
          disabled={isLoading}
        />
        {(value.length > 0 || searchTerm) && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3 py-0"
            onClick={handleClearAll}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {showSuggestions && filteredConferences.length > 0 && (
        <div ref={suggestionsRef} className="absolute z-50 mt-1 w-full rounded-md border bg-popover shadow-md">
          <div ref={areaRef} className="h-auto max-h-60 overflow-y-auto pv-scroll-mini" data-scrolling={isScrolling ? 'true' : 'false'}>
            <div className="p-1 space-y-1">
              {filteredConferences.map(conference => (
                <div
                  key={conference}
                  role="option"
                  tabIndex={0}
                  className="w-full flex items-center gap-2 rounded-sm px-2 py-1.5 text-left hover:bg-accent cursor-pointer"
                  onClick={() => toggleConference(conference)}
                  onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); toggleConference(conference) } }}
                >
                  <Checkbox checked={value.includes(conference)} onCheckedChange={() => toggleConference(conference)} />
                  <span className="text-sm">{conference}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {value.length > 0 && (
        <div className="mt-2 flex flex-wrap gap-1">
          {value.map(conference => (
            <Badge key={conference} variant="secondary" className="text-xs">
              {conference}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={() => toggleConference(conference)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}
