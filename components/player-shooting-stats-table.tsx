"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ChevronUp, ChevronDown } from "lucide-react"

// Stat definitions for tooltips
const statDefinitions: Record<string, string> = {
  // Player Info
  player_name: "Player's full name",
  class: "Player's academic year (<PERSON>, <PERSON>, Jr, Sr)",
  role: "Player's role on the team",
  team_name: "Player's college team",
  gp: "Games Played",
  mpg: "Minutes Per Game",
  
  // Field Goal Stats
  fgm: "Field Goals Made per game",
  fga: "Field Goals Attempted per game",
  fg_pct: "Field Goal Percentage",
  
  // 2-Point Stats
  "2pm": "2-Point Field Goals Made",
  "2pa": "2-Point Field Goals Attempted",
  "2p_pct": "2-Point Field Goal Percentage",
  
  // 3-Point Stats
  "3pm": "Three-Point Field Goals Made per game",
  "3pa": "Three-Point Attempts per game",
  "3p_pct": "Three-Point Percentage",
  "3p_100": "3-Point Attempts per 100 possessions",
  
  // Free Throw Stats
  ftm: "Free Throws Made per game",
  fta: "Free Throws Attempted per game",
  ft_pct: "Free Throw Percentage",
  ftr: "Free Throw Rate - ratio of FTA to FGA",
  
  // Efficiency Stats
  ts_pct: "True Shooting Percentage - accounts for FG, 3P, and FT efficiency",
  efg_pct: "Effective Field Goal Percentage - adjusts FG% to give more weight to 3-pointers",
  total_s_pct: "Total Shooting Percentage - scoring efficiency weighted by usage",
  pps: "Points Per Shot",
  
  // Shot Location Stats
  rim_makes: "Field Goals Made at the Rim",
  rim_attempts: "Field Goals Attempted at the Rim",
  rim_pct: "Rim Field Goal Percentage",
  mid_range_makes: "Mid-Range Field Goals Made",
  mid_range_attempts: "Mid-Range Field Goals Attempted",
  mid_range_pct: "Mid-Range Field Goal Percentage",
  
  season: "Basketball season (e.g., 2023_24)"
}

interface PlayerShootingStatsTableProps {
  players: Array<any>
  selectedPlayerId?: number | null
  onPlayerSelect?: (player: any) => void
}

export function PlayerShootingStatsTable({
  players,
  selectedPlayerId,
  onPlayerSelect
}: PlayerShootingStatsTableProps) {
  const [sortField, setSortField] = useState("fg_pct")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 50

  const filteredPlayers = players

  const sortedPlayers = [...filteredPlayers].sort((a, b) => {
    const getValue = (obj: any, path: string) => {
      return path.split('.').reduce((current, key) => current?.[key], obj)
    }

    let aVal = getValue(a, sortField)
    let bVal = getValue(b, sortField)

    // Handle undefined/null values
    if (aVal === undefined || aVal === null) aVal = 0
    if (bVal === undefined || bVal === null) bVal = 0

    // Convert to numbers if they're numeric strings
    if (typeof aVal === 'string' && !isNaN(Number(aVal))) aVal = Number(aVal)
    if (typeof bVal === 'string' && !isNaN(Number(bVal))) bVal = Number(bVal)

    if (typeof aVal === 'string' && typeof bVal === 'string') {
      return sortDirection === "asc" 
        ? aVal.localeCompare(bVal)
        : bVal.localeCompare(aVal)
    }

    return sortDirection === "asc" ? aVal - bVal : bVal - aVal
  })

  const paginatedPlayers = sortedPlayers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const totalPages = Math.ceil(sortedPlayers.length / itemsPerPage)

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("desc") // Default to descending for shooting stats
    }
  }

  const SortButton = ({ field, children }: { field: string; children: React.ReactNode }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            className="h-auto p-0 font-medium"
            onClick={() => handleSort(field)}
          >
            {children}
            {sortField === field && (
              sortDirection === "asc" ? <ChevronUp className="ml-1 h-3 w-3" /> : <ChevronDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{statDefinitions[field] || field}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )

  const formatStat = (value: any) => {
    if (value === null || value === undefined) return 'N/A'
    if (typeof value === 'number') {
      if (value < 1 && value > 0) {
        return value.toFixed(3)
      }
      return value.toFixed(1)
    }
    return value.toString()
  }

  const formatPercentage = (value: any) => {
    if (value === null || value === undefined) return 'N/A'
    if (typeof value === 'number') {
      return `${(value * 100).toFixed(1)}%`
    }
    return value.toString()
  }

  const formatAlreadyPercentage = (value: any) => {
    if (value === null || value === undefined) return 'N/A'
    if (typeof value === 'number') {
      return `${value.toFixed(1)}%`
    }
    return value.toString()
  }

  return (
    <div className="space-y-4">


      <div className="rounded-md border overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              {/* Player Info */}
              <TableHead className="sticky left-0 top-0 bg-background z-20 w-32 min-w-32 max-w-32"><SortButton field="player_name">Name</SortButton></TableHead>
              <TableHead className="sticky left-[128px] top-0 bg-background z-20 w-20 min-w-20 max-w-20 border-r-0"><SortButton field="class">Class</SortButton></TableHead>
              <TableHead className="sticky left-[208px] top-0 bg-background z-20 w-20 min-w-20 max-w-20 border-l-0"><SortButton field="role">Role</SortButton></TableHead>
              <TableHead className="sticky left-[288px] top-0 bg-background z-20 w-32 min-w-32 max-w-32"><SortButton field="team_name">Team</SortButton></TableHead>
              <TableHead className="sticky left-[416px] top-0 bg-background z-20 w-16 min-w-16 max-w-16"><SortButton field="gp">GP</SortButton></TableHead>
              <TableHead className="sticky left-[480px] top-0 bg-background z-20 w-20 min-w-20 max-w-20"><SortButton field="mpg">MPG</SortButton></TableHead>
              
              {/* Field Goal Stats */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="fgm">FGM</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="fga">FGA</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="fg_pct">FG%</SortButton></TableHead>
              
              {/* 2-Point Stats */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="2pm">2PM</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="2pa">2PA</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="2p_pct">2P%</SortButton></TableHead>
              
              {/* 3-Point Stats */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="3pm">3PM</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="3pa">3PA</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="3p_pct">3P%</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="3p_100">3P/100</SortButton></TableHead>
              
              {/* Free Throw Stats */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="ftm">FTM</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="fta">FTA</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="ft_pct">FT%</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="ftr">FTR</SortButton></TableHead>
              
              {/* Efficiency Stats */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="ts_pct">TS%</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="efg_pct">eFG%</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="total_s_pct">Total S%</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="pps">PPS</SortButton></TableHead>
              
              {/* Shot Location Stats */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="rim_makes">Rim Makes</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="rim_attempts">Rim Att</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="rim_pct">Rim%</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="mid_range_makes">Mid Makes</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="mid_range_attempts">Mid Att</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="mid_range_pct">Mid%</SortButton></TableHead>
              
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="season">Season</SortButton></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedPlayers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={31} className="text-center text-muted-foreground">
                  No players found
                </TableCell>
              </TableRow>
            ) : (
              paginatedPlayers.map((player, index) => {
                return (
                  <TableRow
                    key={player.player_id || player.id || index}
                    className={`cursor-pointer hover:bg-muted/50 ${
                      selectedPlayerId === (player.player_id || player.id) ? "bg-muted" : ""
                    }`}
                    onClick={() => onPlayerSelect?.(player)}
                  >
                    <TableCell className="sticky left-0 bg-background font-medium whitespace-nowrap w-32 min-w-32 max-w-32">
                      {player.player_name || player.name || 'N/A'}
                    </TableCell>
                    <TableCell className="sticky left-[128px] bg-background w-20 min-w-20 max-w-20 border-r-0">{player.class || 'N/A'}</TableCell>
                    <TableCell className="sticky left-[208px] bg-background w-20 min-w-20 max-w-20 border-l-0 text-xs">{player.role || 'N/A'}</TableCell>
                    <TableCell className="sticky left-[288px] bg-background whitespace-nowrap w-32 min-w-32 max-w-32 text-xs">{player.team_name || player.team || 'N/A'}</TableCell>
                    <TableCell className="sticky left-[416px] bg-background w-16 min-w-16 max-w-16">{formatStat(player.gp)}</TableCell>
                    <TableCell className="sticky left-[480px] bg-background w-20 min-w-20 max-w-20">{formatStat(player.mpg)}</TableCell>
                    
                    {/* Field Goal Stats */}
                    <TableCell>{formatStat(player.fgm)}</TableCell>
                    <TableCell>{formatStat(player.fga)}</TableCell>
                    <TableCell>{formatPercentage(player.fg_pct)}</TableCell>
                    
                    {/* 2-Point Stats */}
                    <TableCell>{formatStat(player['2pm'])}</TableCell>
                    <TableCell>{formatStat(player['2pa'])}</TableCell>
                    <TableCell>{formatPercentage(player['2p_pct'])}</TableCell>
                    
                    {/* 3-Point Stats */}
                    <TableCell>{formatStat(player['3pm'])}</TableCell>
                    <TableCell>{formatStat(player['3pa'])}</TableCell>
                    <TableCell>{formatPercentage(player['3p_pct'])}</TableCell>
                    <TableCell>{formatStat(player['3p_100'])}</TableCell>
                    
                    {/* Free Throw Stats */}
                    <TableCell>{formatStat(player.ftm)}</TableCell>
                    <TableCell>{formatStat(player.fta)}</TableCell>
                    <TableCell>{formatPercentage(player.ft_pct)}</TableCell>
                    <TableCell>{formatAlreadyPercentage(player.ftr)}</TableCell>
                    
                    {/* Efficiency Stats */}
                    <TableCell>{formatPercentage(player.ts_pct)}</TableCell>
                    <TableCell>{formatPercentage(player.efg_pct)}</TableCell>
                    <TableCell>{formatAlreadyPercentage(player.total_s_pct)}</TableCell>
                    <TableCell>{formatStat(player.pps)}</TableCell>
                    
                    {/* Shot Location Stats */}
                    <TableCell>{formatStat(player.rim_makes)}</TableCell>
                    <TableCell>{formatStat(player.rim_attempts)}</TableCell>
                    <TableCell>{formatPercentage(player.rim_pct)}</TableCell>
                    <TableCell>{formatStat(player.mid_range_makes)}</TableCell>
                    <TableCell>{formatStat(player.mid_range_attempts)}</TableCell>
                    <TableCell>{formatPercentage(player.mid_range_pct)}</TableCell>
                    
                    <TableCell>{player.season || 'N/A'}</TableCell>
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {Math.min(currentPage * itemsPerPage, filteredPlayers.length)} of {filteredPlayers.length} players
        </p>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="text-sm">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
} 