"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ChevronUp, ChevronDown } from "lucide-react"

// Stat definitions for tooltips
const statDefinitions: Record<string, string> = {
  // Player Info
  player_name: "Player's full name",
  class: "Player's academic year (<PERSON>, <PERSON>, <PERSON>, Sr)",
  role: "Player's role on the team",
  team_name: "Player's college team",
  gp: "Games Played",
  mpg: "Minutes Per Game",
  
  // Minutes & Usage
  min_pct: "Percentage of team minutes the player was on court",
  usg_pct: "Usage Rate - percentage of team plays used by the player",
  
  // Rebounding
  orb_pct: "Offensive Rebound Rate - estimate of available offensive rebounds grabbed",
  drb_pct: "Defensive Rebound Rate",
  trb_pct: "Total Rebound Rate",
  
  // Playmaking
  ast_pct: "Assist Rate - percentage of teammate field goals assisted",
  tov_pct: "Turnover Rate - percentage of possessions ending in a turnover",
  ast_tov: "Assist-to-Turnover Ratio",
  
  // Defense
  stl_pct: "Steal Rate - percentage of opponent possessions ending in a steal",
  blk_pct: "Block Rate",
  
  // Performance
  ppr: "100 x (League Pace / Team Pace) x ([(Assists x 2/3) - Turnovers] / Minutes)",
  personal_foul_rate: "Fouls committed per minute or per 100 possessions",
  
  // Efficiency
  ortg: "Offensive Rating - points produced per 100 possessions",
  drtg: "Defensive Rating - points allowed per 100 possessions",
  ediff: "Efficiency Differential - ORtg minus DRtg",
  
  // Impact Metrics
  fic: "Floor Impact Counter - overall productivity metric",
  porpag: "Points Over Replacement Per Adjusted Game",
  dporpag: "Defensive Points Over Replacement Per Adjusted Game",
  
  // Adjusted Efficiency
  adj_off_efficiency: "Adjusted Offensive Efficiency - points per 100 possessions, adjusted for strength of schedule",
  adj_def_efficiency: "Adjusted Defensive Rating - defensive points per 100 possessions, adjusted for strength of schedule",
  stops: "Estimated defensive stops contributed",
  
  // Overall
  per: "Player Efficiency Rating - per-minute rating of performance",
  
  // Box Plus-Minus
  bpm: "Box Plus-Minus",
  obpm: "Offensive Box Plus-Minus",
  dbpm: "Defensive Box Plus-Minus",
  gbpm: "Generic Box Plus-Minus",
  ogbpm: "Offensive GBPM",
  dgbpm: "Defensive GBPM",
  
  season: "Basketball season (e.g., 2023_24)"
}

interface PlayerAdvancedStatsTableProps {
  players: Array<any>
  selectedPlayerId?: number | null
  onPlayerSelect?: (player: any) => void
}

export function PlayerAdvancedStatsTable({
  players,
  selectedPlayerId,
  onPlayerSelect
}: PlayerAdvancedStatsTableProps) {
  const [sortField, setSortField] = useState("per")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 50

  const filteredPlayers = players

  const sortedPlayers = [...filteredPlayers].sort((a, b) => {
    const getValue = (obj: any, path: string) => {
      return path.split('.').reduce((current, key) => current?.[key], obj)
    }

    let aVal = getValue(a, sortField)
    let bVal = getValue(b, sortField)

    // Handle undefined/null values
    if (aVal === undefined || aVal === null) aVal = 0
    if (bVal === undefined || bVal === null) bVal = 0

    // Convert to numbers if they're numeric strings
    if (typeof aVal === 'string' && !isNaN(Number(aVal))) aVal = Number(aVal)
    if (typeof bVal === 'string' && !isNaN(Number(bVal))) bVal = Number(bVal)

    if (typeof aVal === 'string' && typeof bVal === 'string') {
      return sortDirection === "asc" 
        ? aVal.localeCompare(bVal)
        : bVal.localeCompare(aVal)
    }

    return sortDirection === "asc" ? aVal - bVal : bVal - aVal
  })

  const paginatedPlayers = sortedPlayers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const totalPages = Math.ceil(sortedPlayers.length / itemsPerPage)

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("desc") // Default to descending for advanced stats
    }
  }

  const SortButton = ({ field, children }: { field: string; children: React.ReactNode }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            className="h-auto p-0 font-medium"
            onClick={() => handleSort(field)}
          >
            {children}
            {sortField === field && (
              sortDirection === "asc" ? <ChevronUp className="ml-1 h-3 w-3" /> : <ChevronDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{statDefinitions[field] || field}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )

  const formatStat = (value: any) => {
    if (value === null || value === undefined) return 'N/A'
    if (typeof value === 'number') {
      if (value < 1 && value > 0) {
        return value.toFixed(3)
      }
      return value.toFixed(1)
    }
    return value.toString()
  }

  const formatPercentage = (value: any) => {
    if (value === null || value === undefined) return 'N/A'
    if (typeof value === 'number') {
      return `${(value * 100).toFixed(1)}%`
    }
    return value.toString()
  }

  const formatAlreadyPercentage = (value: any) => {
    if (value === null || value === undefined) return 'N/A'
    if (typeof value === 'number') {
      return `${value.toFixed(1)}%`
    }
    return value.toString()
  }

  return (
    <div className="space-y-4">


      <div className="rounded-md border overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              {/* Player Info */}
              <TableHead className="sticky left-0 top-0 bg-background z-20 w-32 min-w-32 max-w-32"><SortButton field="player_name">Name</SortButton></TableHead>
              <TableHead className="sticky left-[128px] top-0 bg-background z-20 w-20 min-w-20 max-w-20 border-r-0"><SortButton field="class">Class</SortButton></TableHead>
              <TableHead className="sticky left-[208px] top-0 bg-background z-20 w-20 min-w-20 max-w-20 border-l-0"><SortButton field="role">Role</SortButton></TableHead>
              <TableHead className="sticky left-[288px] top-0 bg-background z-20 w-32 min-w-32 max-w-32"><SortButton field="team_name">Team</SortButton></TableHead>
              <TableHead className="sticky left-[416px] top-0 bg-background z-20 w-16 min-w-16 max-w-16"><SortButton field="gp">GP</SortButton></TableHead>
              <TableHead className="sticky left-[480px] top-0 bg-background z-20 w-20 min-w-20 max-w-20"><SortButton field="mpg">MPG</SortButton></TableHead>
              
              {/* Minutes & Usage */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="min_pct">MIN%</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="usg_pct">USG%</SortButton></TableHead>
              
              {/* Rebounding */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="orb_pct">ORB%</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="drb_pct">DRB%</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="trb_pct">TRB%</SortButton></TableHead>
              
              {/* Playmaking */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="ast_pct">AST%</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="tov_pct">TOV%</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="ast_tov">AST/TOV</SortButton></TableHead>
              
              {/* Defense */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="stl_pct">STL%</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="blk_pct">BLK%</SortButton></TableHead>
              
              {/* Performance */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="ppr">PPR</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="personal_foul_rate">PF Rate</SortButton></TableHead>
              
              {/* Efficiency */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="ortg">ORtg</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="drtg">DRtg</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="ediff">eDiff</SortButton></TableHead>
              
              {/* Impact Metrics */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="fic">FIC</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="porpag">PORPAG</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="dporpag">DPORPAG</SortButton></TableHead>
              
              {/* Adjusted Efficiency */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="adj_off_efficiency">Adj OE</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="adj_def_efficiency">Adj DE</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="stops">Stops</SortButton></TableHead>
              
              {/* Overall */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="per">PER</SortButton></TableHead>
              
              {/* Box Plus-Minus */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="bpm">BPM</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="obpm">OBPM</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="dbpm">DBPM</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="gbpm">GBPM</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="ogbpm">OGBPM</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="dgbpm">DGBPM</SortButton></TableHead>
              
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="season">Season</SortButton></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedPlayers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={36} className="text-center text-muted-foreground">
                  No players found
                </TableCell>
              </TableRow>
            ) : (
              paginatedPlayers.map((player, index) => {
                return (
                  <TableRow
                    key={player.player_id || player.id || index}
                    className={`cursor-pointer hover:bg-muted/50 ${
                      selectedPlayerId === (player.player_id || player.id) ? "bg-muted" : ""
                    }`}
                    onClick={() => onPlayerSelect?.(player)}
                  >
                    <TableCell className="sticky left-0 bg-background font-medium whitespace-nowrap w-32 min-w-32 max-w-32">
                      {player.player_name || player.name || 'N/A'}
                    </TableCell>
                    <TableCell className="sticky left-[128px] bg-background w-20 min-w-20 max-w-20 border-r-0">{player.class || 'N/A'}</TableCell>
                    <TableCell className="sticky left-[208px] bg-background w-20 min-w-20 max-w-20 border-l-0 text-xs">{player.role || 'N/A'}</TableCell>
                    <TableCell className="sticky left-[288px] bg-background whitespace-nowrap w-32 min-w-32 max-w-32 text-xs">{player.team_name || player.team || 'N/A'}</TableCell>
                    <TableCell className="sticky left-[416px] bg-background w-16 min-w-16 max-w-16">{formatStat(player.gp)}</TableCell>
                    <TableCell className="sticky left-[480px] bg-background w-20 min-w-20 max-w-20">{formatStat(player.mpg)}</TableCell>
                    
                    {/* Minutes & Usage */}
                    <TableCell>{formatAlreadyPercentage(player.min_pct)}</TableCell>
                    <TableCell>{formatAlreadyPercentage(player.usg_pct)}</TableCell>
                    
                    {/* Rebounding */}
                    <TableCell>{formatAlreadyPercentage(player.orb_pct)}</TableCell>
                    <TableCell>{formatAlreadyPercentage(player.drb_pct)}</TableCell>
                    <TableCell>{formatAlreadyPercentage(player.trb_pct)}</TableCell>
                    
                    {/* Playmaking */}
                    <TableCell>{formatAlreadyPercentage(player.ast_pct)}</TableCell>
                    <TableCell>{formatAlreadyPercentage(player.tov_pct)}</TableCell>
                    <TableCell>{formatStat(player.ast_tov)}</TableCell>
                    
                    {/* Defense */}
                    <TableCell>{formatAlreadyPercentage(player.stl_pct)}</TableCell>
                    <TableCell>{formatAlreadyPercentage(player.blk_pct)}</TableCell>
                    
                    {/* Performance */}
                    <TableCell>{formatStat(player.ppr)}</TableCell>
                    <TableCell>{formatStat(player.personal_foul_rate)}</TableCell>
                    
                    {/* Efficiency */}
                    <TableCell>{formatStat(player.ortg)}</TableCell>
                    <TableCell>{formatStat(player.drtg)}</TableCell>
                    <TableCell>{formatStat(player.ediff)}</TableCell>
                    
                    {/* Impact Metrics */}
                    <TableCell>{formatStat(player.fic)}</TableCell>
                    <TableCell>{formatStat(player.porpag)}</TableCell>
                    <TableCell>{formatStat(player.dporpag)}</TableCell>
                    
                    {/* Adjusted Efficiency */}
                    <TableCell>{formatStat(player.adj_off_efficiency)}</TableCell>
                    <TableCell>{formatStat(player.adj_def_efficiency)}</TableCell>
                    <TableCell>{formatStat(player.stops)}</TableCell>
                    
                    {/* Overall */}
                    <TableCell>{formatStat(player.per)}</TableCell>
                    
                    {/* Box Plus-Minus */}
                    <TableCell>{formatStat(player.bpm)}</TableCell>
                    <TableCell>{formatStat(player.obpm)}</TableCell>
                    <TableCell>{formatStat(player.dbpm)}</TableCell>
                    <TableCell>{formatStat(player.gbpm)}</TableCell>
                    <TableCell>{formatStat(player.ogbpm)}</TableCell>
                    <TableCell>{formatStat(player.dgbpm)}</TableCell>
                    
                    <TableCell>{player.season || 'N/A'}</TableCell>
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {Math.min(currentPage * itemsPerPage, filteredPlayers.length)} of {filteredPlayers.length} players
        </p>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="text-sm">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
} 