"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ChevronUp, ChevronDown } from "lucide-react"

// Stat definitions for tooltips
const statDefinitions: Record<string, string> = {
  // Player Info
  player_name: "Player's full name",
  class: "Player's academic year (<PERSON>, <PERSON>, Jr, Sr)",
  role: "Player's role on the team",
  team_name: "Player's college team",
  
  // Basic Stats
  gp: "Games Played",
  mpg: "Minutes Per Game",
  ppg: "Points Per Game",
  orb: "Offensive Rebounds per game",
  drb: "Defensive Rebounds per game",
  rpg: "Rebounds Per Game",
  apg: "Assists Per Game",
  spg: "Steals Per Game",
  bpg: "Blocks Per Game",
  tov: "Turnovers per game",
  pf: "Personal Fouls per game",
  ppm: "Points Per Minute",
  apm: "Assists Per Minute",
  rpm: "Rebounds Per Minute",
  season: "Basketball season (e.g., 2023_24)"
}

interface PlayerBasicStatsTableProps {
  players: Array<any>
  selectedPlayerId?: number | null
  onPlayerSelect?: (player: any) => void
}

export function PlayerBasicStatsTable({
  players,
  selectedPlayerId,
  onPlayerSelect
}: PlayerBasicStatsTableProps) {
  const [sortField, setSortField] = useState("ppg")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 50

  const filteredPlayers = players

  const sortedPlayers = [...filteredPlayers].sort((a, b) => {
    const getValue = (obj: any, path: string) => {
      return path.split('.').reduce((current, key) => current?.[key], obj)
    }

    let aVal = getValue(a, sortField)
    let bVal = getValue(b, sortField)

    // Handle undefined/null values
    if (aVal === undefined || aVal === null) aVal = 0
    if (bVal === undefined || bVal === null) bVal = 0

    // Convert to numbers if they're numeric strings
    if (typeof aVal === 'string' && !isNaN(Number(aVal))) aVal = Number(aVal)
    if (typeof bVal === 'string' && !isNaN(Number(bVal))) bVal = Number(bVal)

    if (typeof aVal === 'string' && typeof bVal === 'string') {
      return sortDirection === "asc" 
        ? aVal.localeCompare(bVal)
        : bVal.localeCompare(aVal)
    }

    return sortDirection === "asc" ? aVal - bVal : bVal - aVal
  })

  const paginatedPlayers = sortedPlayers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const totalPages = Math.ceil(sortedPlayers.length / itemsPerPage)

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("desc") // Default to descending for stats
    }
  }

  const SortButton = ({ field, children }: { field: string; children: React.ReactNode }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            className="h-auto p-0 font-medium"
            onClick={() => handleSort(field)}
          >
            {children}
            {sortField === field && (
              sortDirection === "asc" ? <ChevronUp className="ml-1 h-3 w-3" /> : <ChevronDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{statDefinitions[field] || field}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )

  const formatStat = (value: any) => {
    if (value === null || value === undefined) return 'N/A'
    if (typeof value === 'number') {
      if (value < 1 && value > 0) {
        return value.toFixed(3)
      }
      return value.toFixed(1)
    }
    return value.toString()
  }

  return (
    <div className="space-y-4">


      <div className="rounded-md border overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              {/* Player Info */}
              <TableHead className="sticky left-0 top-0 bg-background z-20 w-32 min-w-32 max-w-32"><SortButton field="player_name">Name</SortButton></TableHead>
              <TableHead className="sticky left-[128px] top-0 bg-background z-20 w-20 min-w-20 max-w-20 border-r-0"><SortButton field="class">Class</SortButton></TableHead>
              <TableHead className="sticky left-[208px] top-0 bg-background z-20 w-20 min-w-20 max-w-20 border-l-0"><SortButton field="role">Role</SortButton></TableHead>
              <TableHead className="sticky left-[288px] top-0 bg-background z-20 w-32 min-w-32 max-w-32"><SortButton field="team_name">Team</SortButton></TableHead>
              <TableHead className="sticky left-[416px] top-0 bg-background z-20 w-16 min-w-16 max-w-16"><SortButton field="gp">GP</SortButton></TableHead>
              <TableHead className="sticky left-[480px] top-0 bg-background z-20 w-20 min-w-20 max-w-20"><SortButton field="mpg">MPG</SortButton></TableHead>
              
              {/* Basic Stats */}
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="ppg">PPG</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="orb">ORB</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="drb">DRB</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="rpg">RPG</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="apg">APG</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="spg">SPG</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="bpg">BPG</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="tov">TOV</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="pf">PF</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="ppm">PPM</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="apm">APM</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="rpm">RPM</SortButton></TableHead>
              <TableHead className="sticky top-0 bg-background z-10"><SortButton field="season">Season</SortButton></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedPlayers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={19} className="text-center text-muted-foreground">
                  No players found
                </TableCell>
              </TableRow>
            ) : (
              paginatedPlayers.map((player, index) => {
                return (
                  <TableRow
                    key={player.player_id || player.id || index}
                    className={`cursor-pointer hover:bg-muted/50 ${
                      selectedPlayerId === (player.player_id || player.id) ? "bg-muted" : ""
                    }`}
                    onClick={() => onPlayerSelect?.(player)}
                  >
                    <TableCell className="sticky left-0 bg-background font-medium whitespace-nowrap w-32 min-w-32 max-w-32">
                      {player.player_name || player.name || 'N/A'}
                    </TableCell>
                    <TableCell className="sticky left-[128px] bg-background w-20 min-w-20 max-w-20 border-r-0">{player.class || 'N/A'}</TableCell>
                    <TableCell className="sticky left-[208px] bg-background w-20 min-w-20 max-w-20 border-l-0 text-xs">{player.role || 'N/A'}</TableCell>
                    <TableCell className="sticky left-[288px] bg-background whitespace-nowrap w-32 min-w-32 max-w-32 text-xs">{player.team_name || player.team || 'N/A'}</TableCell>
                    <TableCell className="sticky left-[416px] bg-background w-16 min-w-16 max-w-16">{formatStat(player.gp)}</TableCell>
                    <TableCell className="sticky left-[480px] bg-background w-20 min-w-20 max-w-20">{formatStat(player.mpg)}</TableCell>
                    <TableCell>{formatStat(player.ppg)}</TableCell>
                    <TableCell>{formatStat(player.orb)}</TableCell>
                    <TableCell>{formatStat(player.drb)}</TableCell>
                    <TableCell>{formatStat(player.rpg)}</TableCell>
                    <TableCell>{formatStat(player.apg)}</TableCell>
                    <TableCell>{formatStat(player.spg)}</TableCell>
                    <TableCell>{formatStat(player.bpg)}</TableCell>
                    <TableCell>{formatStat(player.tov)}</TableCell>
                    <TableCell>{formatStat(player.pf)}</TableCell>
                    <TableCell>{formatStat(player.ppm)}</TableCell>
                    <TableCell>{formatStat(player.apm)}</TableCell>
                    <TableCell>{formatStat(player.rpm)}</TableCell>
                    <TableCell>{player.season || 'N/A'}</TableCell>
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {Math.min(currentPage * itemsPerPage, filteredPlayers.length)} of {filteredPlayers.length} players
        </p>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="text-sm">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
} 