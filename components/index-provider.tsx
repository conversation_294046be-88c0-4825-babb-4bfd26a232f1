"use client"

import { createContext, useContext, useMemo, useState, ReactNode, useCallback, useEffect } from "react"
import { Player } from "@/types/player"
import { IndexWeights, computeCustomIndexForAll, defaultWeights, STAT_DEFS } from "@/utils/custom-index"

interface IndexContextType {
  useCustomIndex: boolean
  indexName: string
  weights: IndexWeights
  setWeights: (w: IndexWeights) => void
  setUseCustomIndex: (v: boolean) => void
  setIndexName: (name: string) => void
  applyIndexToPlayers: (players: Player[]) => Player[]
  resetToDefault: () => void
  statDefs: typeof STAT_DEFS
  savedIndexes: SavedIndex[]
  saveIndex: (name: string, weights: IndexWeights) => void
  deleteIndex: (name: string) => void
  applySavedIndex: (name: string) => void
  useSeasonIndex: boolean
  setUseSeasonIndex: (v: boolean) => void
}

export interface SavedIndex {
  name: string
  weights: IndexWeights
  createdAt: number
}

const IndexContext = createContext<IndexContextType | undefined>(undefined)

export function IndexProvider({ children }: { children: ReactNode }) {
  const [useCustomIndex, setUseCustomIndex] = useState(false)
  const [indexName, setIndexName] = useState("Custom Index")
  const [weights, setWeights] = useState<IndexWeights>(defaultWeights())
  const [savedIndexes, setSavedIndexes] = useState<SavedIndex[]>([])
  const [useSeasonIndex, setUseSeasonIndex] = useState(false)

  // local persistence
  useEffect(() => {
    try {
      const raw = typeof window !== 'undefined' ? localStorage.getItem('pv_saved_indexes') : null
      if (raw) setSavedIndexes(JSON.parse(raw))
    } catch {}
  }, [])

  useEffect(() => {
    try {
      if (typeof window !== 'undefined') localStorage.setItem('pv_saved_indexes', JSON.stringify(savedIndexes))
    } catch {}
  }, [savedIndexes])

  const applyIndexToPlayers = useCallback(
    (players: Player[]) => {
      if (!useCustomIndex) return players
      return computeCustomIndexForAll(players, weights)
    },
    [useCustomIndex, weights]
  )

  const resetToDefault = () => setWeights(defaultWeights())

  const saveIndex = (name: string, w: IndexWeights) => {
    const trimmed = (name || 'Custom Index').trim()
    setSavedIndexes((prev) => {
      const others = prev.filter((i) => i.name !== trimmed)
      return [...others, { name: trimmed, weights: { ...w }, createdAt: Date.now() }]
    })
  }

  const deleteIndex = (name: string) => {
    setSavedIndexes((prev) => prev.filter((i) => i.name !== name))
  }

  const applySavedIndex = (name: string) => {
    const found = savedIndexes.find((i) => i.name === name)
    if (!found) return
    setWeights(found.weights)
    setIndexName(found.name)
    setUseCustomIndex(true)
  }

  const value: IndexContextType = useMemo(() => ({
    useCustomIndex,
    indexName,
    weights,
    setWeights,
    setUseCustomIndex,
    setIndexName,
    applyIndexToPlayers,
    resetToDefault,
    statDefs: STAT_DEFS,
    savedIndexes,
    saveIndex,
    deleteIndex,
    applySavedIndex,
    useSeasonIndex,
    setUseSeasonIndex,
  }), [useCustomIndex, indexName, weights, applyIndexToPlayers, savedIndexes, useSeasonIndex])

  return (
    <IndexContext.Provider value={value}>{children}</IndexContext.Provider>
  )
}

export function useIndex() {
  const ctx = useContext(IndexContext)
  if (!ctx) throw new Error("useIndex must be used within IndexProvider")
  return ctx
}


