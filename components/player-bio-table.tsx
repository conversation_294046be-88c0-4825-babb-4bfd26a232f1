"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ChevronUp, ChevronDown } from "lucide-react"

interface PlayerBioTableProps {
  players: Array<any>
  selectedPlayerId?: number | null
  onPlayerSelect?: (player: any) => void
}

export function PlayerBioTable({
  players,
  selectedPlayerId,
  onPlayerSelect
}: PlayerBioTableProps) {
  const [sortField, setSortField] = useState("player_name")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 50

  const filteredPlayers = players

  const sortedPlayers = [...filteredPlayers].sort((a, b) => {
    const getValue = (obj: any, path: string) => {
      return path.split('.').reduce((current, key) => current?.[key], obj)
    }

    let aVal = getValue(a, sortField)
    let bVal = getValue(b, sortField)

    // Handle undefined/null values
    if (aVal === undefined || aVal === null) aVal = 0
    if (bVal === undefined || bVal === null) bVal = 0

    // Convert to numbers if they're numeric strings
    if (typeof aVal === 'string' && !isNaN(Number(aVal))) aVal = Number(aVal)
    if (typeof bVal === 'string' && !isNaN(Number(bVal))) bVal = Number(bVal)

    if (typeof aVal === 'string' && typeof bVal === 'string') {
      return sortDirection === "asc" 
        ? aVal.localeCompare(bVal)
        : bVal.localeCompare(aVal)
    }

    return sortDirection === "asc" ? aVal - bVal : bVal - aVal
  })

  const paginatedPlayers = sortedPlayers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const totalPages = Math.ceil(sortedPlayers.length / itemsPerPage)

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  const SortButton = ({ field, children }: { field: string; children: React.ReactNode }) => (
    <Button
      variant="ghost"
      className="h-auto p-0 font-medium"
      onClick={() => handleSort(field)}
    >
      {children}
      {sortField === field && (
        sortDirection === "asc" ? <ChevronUp className="ml-1 h-3 w-3" /> : <ChevronDown className="ml-1 h-3 w-3" />
      )}
    </Button>
  )

  const formatStat = (value: any) => {
    if (value === null || value === undefined) return 'N/A'
    if (typeof value === 'number') {
      if (value < 1 && value > 0) {
        return value.toFixed(3)
      }
      return value.toFixed(1)
    }
    return value.toString()
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              {/* Basic Info */}
              <TableHead className="sticky left-0 bg-background z-10"><SortButton field="player_name">Name</SortButton></TableHead>
              <TableHead><SortButton field="team_name">Team</SortButton></TableHead>
              <TableHead><SortButton field="conference_name">Conference</SortButton></TableHead>
              <TableHead><SortButton field="position">Position</SortButton></TableHead>
              <TableHead><SortButton field="role">Role</SortButton></TableHead>
              <TableHead><SortButton field="class">Class</SortButton></TableHead>
              <TableHead><SortButton field="number">Number</SortButton></TableHead>
              <TableHead><SortButton field="height">Height</SortButton></TableHead>
              <TableHead><SortButton field="weight">Weight</SortButton></TableHead>
              <TableHead><SortButton field="age">Age</SortButton></TableHead>
              <TableHead><SortButton field="gp">GP</SortButton></TableHead>
              <TableHead><SortButton field="mpg">MPG</SortButton></TableHead>
              <TableHead><SortButton field="pick">Pick</SortButton></TableHead>
              <TableHead><SortButton field="nba_team">NBA Team</SortButton></TableHead>
              <TableHead><SortButton field="transfer">Transfer</SortButton></TableHead>
              <TableHead><SortButton field="rec_rank">Rec Rank</SortButton></TableHead>
              <TableHead><SortButton field="season">Season</SortButton></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedPlayers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={17} className="text-center text-muted-foreground">
                  No players found
                </TableCell>
              </TableRow>
            ) : (
              paginatedPlayers.map((player, index) => {
                return (
                  <TableRow
                    key={player.player_id || player.id || index}
                    className={`cursor-pointer hover:bg-muted/50 ${
                      selectedPlayerId === (player.player_id || player.id) ? "bg-muted" : ""
                    }`}
                    onClick={() => onPlayerSelect?.(player)}
                  >
                    <TableCell className="sticky left-0 bg-background font-medium whitespace-nowrap">
                      {player.player_name || player.name || 'N/A'}
                    </TableCell>
                    <TableCell className="whitespace-nowrap">{player.team_name || player.team || 'N/A'}</TableCell>
                    <TableCell>{player.conference_name || player.conference || 'N/A'}</TableCell>
                    <TableCell>{player.position || 'N/A'}</TableCell>
                    <TableCell>{player.role || 'N/A'}</TableCell>
                    <TableCell>{player.class || 'N/A'}</TableCell>
                    <TableCell>{formatStat(player.number)}</TableCell>
                    <TableCell>{player.height || 'N/A'}</TableCell>
                    <TableCell>{formatStat(player.weight)}</TableCell>
                    <TableCell>{formatStat(player.age)}</TableCell>
                    <TableCell>{formatStat(player.gp || player.stats?.gp)}</TableCell>
                    <TableCell>{formatStat(player.mpg || player.stats?.mpg)}</TableCell>
                    <TableCell>{formatStat(player.pick)}</TableCell>
                    <TableCell>{player.nba_team || 'N/A'}</TableCell>
                    <TableCell>{player.transfer ? 'Yes' : 'No'}</TableCell>
                    <TableCell>{formatStat(player.rec_rank)}</TableCell>
                    <TableCell>{player.season || 'N/A'}</TableCell>
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {Math.min(currentPage * itemsPerPage, filteredPlayers.length)} of {filteredPlayers.length} players
        </p>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="text-sm">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
} 