import React from 'react'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

type NumericFilterKey = string;

interface HeightDropdownFilterProps {
  min: number
  max: number
  minKey: NumericFilterKey
  maxKey: NumericFilterKey
  filters: Record<string, any>
  setFilters: any // Allow any type of state setter function
}

export function HeightDropdownFilter({ min, max, minKey, maxKey, filters, setFilters }: HeightDropdownFilterProps) {
  // Get current filter values
  const minHeightInches = typeof filters[minKey] === 'number' ? filters[minKey] as number : min;
  const maxHeightInches = typeof filters[maxKey] === 'number' ? filters[maxKey] as number : max;

  // Calculate feet and inches for display
  const minFeet = Math.floor(minHeightInches / 12);
  const minInches = minHeightInches % 12;
  const maxFeet = Math.floor(maxHeightInches / 12);
  const maxInches = maxHeightInches % 12;

  // Update the filter when feet or inches change
  const updateMinHeight = (feet: number, inches: number) => {
    const totalInches = (feet * 12) + inches;
    setFilters((prev: Record<string, any>) => ({ ...prev, [minKey]: totalInches }));
  };

  const updateMaxHeight = (feet: number, inches: number) => {
    const totalInches = (feet * 12) + inches;
    setFilters((prev: Record<string, any>) => ({ ...prev, [maxKey]: totalInches }));
  };

  return (
    <div className="space-y-2">
      <Label>Height</Label>
      <div className="flex gap-2">
        <div className="flex-1">
          <Label className="text-xs text-muted-foreground mb-1 block">
            Min
          </Label>
          <div className="flex gap-1">
            <div className="w-1/2">
              <Select
                value={minFeet.toString()}
                onValueChange={(value) => {
                  const feet = parseInt(value, 10);
                  updateMinHeight(feet, minInches);
                }}
              >
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="ft" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 5 }, (_, i) => i + 4).map((feet) => (
                    <SelectItem key={`min-feet-${feet}`} value={feet.toString()}>
                      {feet} ft
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="w-1/2">
              <Select
                value={minInches.toString()}
                onValueChange={(value) => {
                  const inches = parseInt(value, 10);
                  updateMinHeight(minFeet, inches);
                }}
              >
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="in" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 12 }, (_, i) => i).map((inches) => (
                    <SelectItem key={`min-inches-${inches}`} value={inches.toString()}>
                      {inches} in
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <div className="flex-1">
          <Label className="text-xs text-muted-foreground mb-1 block">
            Max
          </Label>
          <div className="flex gap-1">
            <div className="w-1/2">
              <Select
                value={maxFeet.toString()}
                onValueChange={(value) => {
                  const feet = parseInt(value, 10);
                  updateMaxHeight(feet, maxInches);
                }}
              >
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="ft" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 5 }, (_, i) => i + 4).map((feet) => (
                    <SelectItem key={`max-feet-${feet}`} value={feet.toString()}>
                      {feet} ft
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="w-1/2">
              <Select
                value={maxInches.toString()}
                onValueChange={(value) => {
                  const inches = parseInt(value, 10);
                  updateMaxHeight(maxFeet, inches);
                }}
              >
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="in" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 12 }, (_, i) => i).map((inches) => (
                    <SelectItem key={`max-inches-${inches}`} value={inches.toString()}>
                      {inches} in
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
