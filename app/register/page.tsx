"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Image from "next/image"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { PasswordInput } from "@/components/ui/password-input"
import { Label } from "@/components/ui/label"
import { UserPlus, Mail, Lock, User, AlertCircle, CheckCircle } from "lucide-react"
import Link from "next/link"
import { useAuth } from "@/components/auth-provider"

export default function RegisterPage() {
  const router = useRouter()
  const { signup, checkEmailAllowed } = useAuth()
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)
  const [emailChecked, setEmailChecked] = useState(false)
  const [emailAllowed, setEmailAllowed] = useState(false)

  // Check if email is allowed to register
  const handleCheckEmail = async () => {
    if (!email || !email.includes('@')) {
      setError("Please enter a valid email address")
      return
    }

    setIsLoading(true)
    setError("")
    setEmailChecked(false)
    setEmailAllowed(false)

    try {
      const { allowed, message } = await checkEmailAllowed(email)
      setEmailChecked(true)
      setEmailAllowed(allowed)

      if (!allowed) {
        setError(message || "This email is not authorized for registration")
      }
    } catch (err) {
      console.error("Email check error:", err)
      setError("Failed to verify email authorization. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    // Basic validation
    if (password !== confirmPassword) {
      setError("Passwords do not match")
      setIsLoading(false)
      return
    }

    if (!emailChecked || !emailAllowed) {
      setError("Please verify your email is authorized for registration first")
      setIsLoading(false)
      return
    }

    try {
      // Call the signup function from our auth context
      const { error, needsEmailVerification } = await signup(email, password, name)

      if (error) {
        setError(error.message || "Failed to create account. Please try again.")
        return
      }

      if (needsEmailVerification) {
        setSuccess(true)
      } else {
        // If email verification is not required, redirect to the login page
        router.push("/login?registered=true")
      }
    } catch (err: any) {
      console.error("Registration error:", err)
      setError(err.message || "Failed to create account. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-background flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-md space-y-8">
        <div className="flex flex-col items-center text-center">
          <Image
            src="/logo-trans.png"
            alt="PlayVision Logo"
            width={100}
            height={100}
            className="mb-4"
          />
          <h1 className="text-3xl font-bold">PlayVision Player Portal</h1>
          <p className="text-muted-foreground mt-2">Create an account to access player statistics and data</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Create Account</CardTitle>
            <CardDescription>
              Enter your information to create a new account
            </CardDescription>
          </CardHeader>
          <CardContent>
            {success ? (
              <div className="space-y-4">
                <div className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 p-4 rounded-md flex items-start">
                  <CheckCircle className="h-5 w-5 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium">Registration successful!</h3>
                    <p className="mt-1 text-sm">
                      We've sent a confirmation email to <strong>{email}</strong>. Please check your inbox and click the verification link to complete your registration.
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push("/login")}
                >
                  Return to Login
                </Button>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md flex items-center">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    {error}
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="name"
                      type="text"
                      placeholder="John Doe"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => {
                          setEmail(e.target.value)
                          setEmailChecked(false)
                          setEmailAllowed(false)
                        }}
                        className="pl-10"
                        required
                        disabled={emailChecked && emailAllowed}
                      />
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCheckEmail}
                      disabled={isLoading || (emailChecked && emailAllowed)}
                      className="whitespace-nowrap"
                    >
                      {emailChecked && emailAllowed ? (
                        <>
                          <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                          Verified
                        </>
                      ) : (
                        'Verify Email'
                      )}
                    </Button>
                  </div>
                  {emailChecked && emailAllowed && (
                    <p className="text-xs text-green-500 flex items-center">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Email verified and authorized for registration
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground z-10" />
                    <PasswordInput
                      id="password"
                      placeholder="••••••••"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="pl-10"
                      required
                      minLength={8}
                      disabled={!emailAllowed}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Password must be at least 8 characters long
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground z-10" />
                    <PasswordInput
                      id="confirmPassword"
                      placeholder="••••••••"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="pl-10"
                      required
                      disabled={!emailAllowed}
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isLoading || !emailAllowed}
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                      Creating account...
                    </>
                  ) : (
                    <>
                      <UserPlus className="h-4 w-4 mr-2" />
                      Create Account
                    </>
                  )}
                </Button>
              </form>
            )}
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <div className="text-sm text-center text-muted-foreground">
              Already have an account?{" "}
              <Link href="/login" className="text-primary hover:underline">
                Sign in
              </Link>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
