import "@/app/globals.css"
import { Inter } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"
import { MainNav } from "@/components/main-nav"
import { AuthProvider } from "@/components/auth-provider"
import { IndexProvider } from "@/components/index-provider"

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "Player Portal",
  description: "Access player statistics and data",
  generator: "v0.dev"
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className} dark min-h-screen w-full max-w-full overflow-x-hidden`}>
        <ThemeProvider defaultTheme="dark" forcedTheme="dark">
          <AuthProvider>
            <IndexProvider>
              <MainNav />
              {children}
            </IndexProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
