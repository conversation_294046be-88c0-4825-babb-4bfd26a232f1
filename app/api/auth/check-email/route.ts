import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request: Request) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    const normalizedEmail = email.toLowerCase().trim()

    // Check if the email exists in the allowed_emails table
    const { data: allowedData, error: allowedError } = await supabase
      .from('allowed_emails')
      .select('*')
      .eq('email', normalizedEmail)
      .single()

    if (allowedError) {
      console.error('Error checking email:', allowedError)

      // If the error is "No rows found", it means the email is not allowed
      if (allowedError.code === 'PGRST116') {
        return NextResponse.json(
          { allowed: false, message: 'Email not authorized for registration' },
          { status: 403 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to check email authorization' },
        { status: 500 }
      )
    }

    // If we get here, the email is allowed
    return NextResponse.json({ allowed: true })
  } catch (error) {
    console.error('Error in check-email route:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
