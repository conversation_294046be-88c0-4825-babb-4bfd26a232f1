import { createClient } from '@supabase/supabase-js'
import { NextResponse } from 'next/server'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabase = createClient(supabaseUrl, supabaseKey)

export async function GET() {
  try {
    const CHUNK = 1000
    let offset = 0
    const seasonsSet = new Set<string>()
    // eslint-disable-next-line no-constant-condition
    while (true) {
      const { data, error } = await supabase
        .from('player_bio')
        .select('season')
        .order('season', { ascending: false })
        .range(offset, offset + CHUNK - 1)

      if (error) throw error
      const rows = data || []
      for (const r of rows) {
        if (r.season) seasonsSet.add(r.season as string)
      }
      if (rows.length < CHUNK) break
      offset += CHUNK
    }

    const seasons = Array.from(seasonsSet).sort((a, b) => (a < b ? 1 : a > b ? -1 : 0))

    return NextResponse.json({ seasons })
  } catch (error) {
    console.error('Error fetching seasons:', error)
    return NextResponse.json({ seasons: [] }, { status: 500 })
  }
} 