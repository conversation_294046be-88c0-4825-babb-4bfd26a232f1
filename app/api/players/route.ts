import { createClient } from '@supabase/supabase-js'
import { NextResponse } from 'next/server'

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabase = createClient(supabaseUrl, supabaseKey)

export async function GET(request: Request) {
  try {
    const url = new URL(request.url)
    const params = Object.fromEntries(url.searchParams.entries()) as {
      season?: string
      searchTerm?: string
      view?: 'bio' | 'basic' | 'shooting' | 'advanced' | 'all'
    }

    console.log('API called with params:', params)

    // Determine season: use provided or most recent available
    let season = params.season
    if (!season) {
      console.log('No season provided, trying to get latest season...')
      const { data: latest, error: latestErr } = await supabase
        .from('player_bio')
        .select('season')
        .order('season', { ascending: false })
        .limit(1)
        .maybeSingle()
      
      if (latestErr) {
        console.error('Error getting latest season:', latestErr)
        return NextResponse.json({ error: `Failed to get latest season: ${latestErr.message}` }, { status: 500 })
      }
      
      season = latest?.season || undefined
      console.log('Latest season found:', season)
    }

    // Helper to fetch all rows with pagination
    const CHUNK = 1000
    const fetchAllFromView = async (viewName: string, selectColumns: string = '*'): Promise<any[]> => {
      let offset = 0
      const all: any[] = []
      console.log(`Fetching ${viewName} data...`)
      
      // eslint-disable-next-line no-constant-condition
      while (true) {
        let query = supabase.from(viewName).select(selectColumns)
        if (season) query = query.eq('season', season)
        const { data, error } = await query.range(offset, offset + CHUNK - 1)
        
        if (error) {
          console.error(`Error fetching ${viewName} chunk:`, error)
          throw error
        }
        
        const batch = data || []
        all.push(...batch)
        console.log(`Fetched ${viewName} chunk ${offset}-${offset + batch.length - 1}, got ${batch.length} records`)
        
        if (batch.length < CHUNK) break
        offset += CHUNK
      }
      return all
    }

    // Fetch data based on requested view or all views for comprehensive data
    let playerData: any = {}
    
    if (params.view === 'bio') {
      const bioData = await fetchAllFromView('player_bio')
      playerData.bio = bioData
    } else if (params.view === 'basic') {
      const basicData = await fetchAllFromView('player_basic_stats')
      playerData.basic = basicData
    } else if (params.view === 'shooting') {
      const shootingData = await fetchAllFromView('player_shooting_stats')
      playerData.shooting = shootingData
    } else if (params.view === 'advanced') {
      const advancedData = await fetchAllFromView('player_advanced')
      playerData.advanced = advancedData
    } else {
      // Fetch all views for comprehensive data (default behavior)
      const [bioData, basicData, shootingData, advancedData] = await Promise.all([
        fetchAllFromView('player_bio'),
        fetchAllFromView('player_basic_stats'),
        fetchAllFromView('player_shooting_stats'),
        fetchAllFromView('player_advanced')
      ])
      
      // Fetch scores data separately since it's a table, not a view
      let scoresData: any[] = []
      try {
        console.log(`Fetching scores data for season: ${season}`)
        
        // ONLY fetch scores for the specific season we need
        const { data: scores, error: scoresError } = await supabase
          .from('scores')
          .select('player_id, season, all_time_ppi, all_time_poi, all_time_pdi, season_ppi, season_poi, season_pdi')
          .eq('season', season) // Only get scores for the current season
        
        if (scoresError) {
          console.error('Error fetching scores data:', scoresError)
        } else {
          scoresData = scores || []
          console.log(`Fetched ${scoresData.length} scores records for season ${season}`)
          
          if (scoresData.length > 0) {
            console.log('Sample scores data:', scoresData.slice(0, 3))
            // Verify all scores are for the correct season
            const wrongSeasonScores = scoresData.filter(s => s.season !== season)
            if (wrongSeasonScores.length > 0) {
              console.error(`ERROR: Found ${wrongSeasonScores.length} scores records with wrong season!`)
              console.error('Wrong season scores:', wrongSeasonScores.slice(0, 3))
            }
          }
        }
      } catch (error) {
        console.error('Error fetching scores data:', error)
      }
      
      // Fetch player_info data separately since it's a table, not a view
      let playerInfoData: any[] = []
      try {
        console.log(`Fetching player_info data for season: ${season}`)
        
        // ONLY fetch player_info for the specific season we need
        const { data: playerInfo, error: playerInfoError } = await supabase
          .from('player_info')
          .select('player_id, season, adj_pps')
          .eq('season', season) // Only get player_info for the current season
        
        if (playerInfoError) {
          console.error('Error fetching player_info data:', playerInfoError)
        } else {
          playerInfoData = playerInfo || []
          console.log(`Fetched ${playerInfoData.length} player_info records for season ${season}`)
          
          if (playerInfoData.length > 0) {
            console.log('Sample player_info data:', playerInfoData.slice(0, 3))
            // Verify all player_info are for the correct season
            const wrongSeasonPlayerInfo = playerInfoData.filter(p => p.season !== season)
            if (wrongSeasonPlayerInfo.length > 0) {
              console.error(`ERROR: Found ${wrongSeasonPlayerInfo.length} player_info records with wrong season!`)
              console.error('Wrong season player_info:', wrongSeasonPlayerInfo.slice(0, 3))
            }
          }
        }
      } catch (error) {
        console.error('Error fetching player_info data:', error)
      }
      
      playerData.bio = bioData
      playerData.basic = basicData
      playerData.shooting = shootingData
      playerData.advanced = advancedData
      playerData.scores = scoresData
      playerData.playerInfo = playerInfoData
    }

    // If fetching comprehensive data, merge all views by player_id
    if (!params.view || params.view === 'all') {
      const mergedPlayers: any[] = []
      const playerMap = new Map<number, any>()

      // Start with bio data as the base
      playerData.bio?.forEach((player: any) => {
        playerMap.set(player.player_id, { ...player })
        
        // Debug: Check if this is player 8443
        if (player.player_id === 8443) {
          console.log('Found player 8443 in bio data:', player)
        }
      })

      // Merge basic stats
      playerData.basic?.forEach((stats: any) => {
        const player = playerMap.get(stats.player_id)
        if (player) {
          Object.assign(player, stats)
        }
      })

      // Merge shooting stats
      playerData.shooting?.forEach((stats: any) => {
        const player = playerMap.get(stats.player_id)
        if (player) {
          Object.assign(player, stats)
        }
      })

      // Merge advanced stats
      playerData.advanced?.forEach((stats: any) => {
        const player = playerMap.get(stats.player_id)
        if (player) {
          Object.assign(player, stats)
        }
      })

      // Merge scores data
      console.log(`Merging ${playerData.scores?.length || 0} scores records with ${playerMap.size} players`)
      console.log(`Current season being processed: ${season}`)
      
      let scoresMatched = 0
      let wrongSeasonScores = 0
      
      playerData.scores?.forEach((scores: any) => {
        // CRITICAL: Double-check that we're only merging scores for the current season
        if (scores.season !== season) {
          console.error(`ERROR: Attempting to merge scores for wrong season! Expected: ${season}, Got: ${scores.season}`)
          wrongSeasonScores++
          return // Skip this record
        }
        
        const player = playerMap.get(scores.player_id)
        if (player) {
          player.scores = scores
          scoresMatched++
          
          // Debug: Check if this is player 8443
          if (scores.player_id === 8443) {
            console.log('Found player 8443 in scores data:', scores)
            console.log('Matching with player in map:', player)
            console.log('Current season being processed:', season)
          }
        }
      })
      
      console.log(`Successfully matched ${scoresMatched} scores records with players`)
      if (wrongSeasonScores > 0) {
        console.error(`WARNING: Skipped ${wrongSeasonScores} scores records with wrong season`)
      }

      // Merge player info data (adj_pps)
      playerData.playerInfo?.forEach((info: any) => {
        const player = playerMap.get(info.player_id)
        if (player) {
          if (!player.advancedStats) player.advancedStats = {}
          player.advancedStats.adj_pps = info.adj_pps
        }
      })

      mergedPlayers.push(...playerMap.values())
      
      // Debug: Check final merged data for player 8443
      const player8443Final = mergedPlayers.find(p => p.player_id === 8443)
      if (player8443Final) {
        console.log('Final merged data for player 8443:', {
          player_id: player8443Final.player_id,
          name: player8443Final.player_name,
          scores: player8443Final.scores,
          adj_pps: player8443Final.advancedStats?.adj_pps
        })
      }

      // Collect team, conference, and player IDs for additional data
      const teamIds = Array.from(new Set(mergedPlayers.map(p => p.team_id).filter(Boolean))) as number[]
      const conferenceIds = Array.from(new Set(mergedPlayers.map(p => p.conference_id).filter(Boolean))) as number[]
      const playerIds = Array.from(new Set(mergedPlayers.map(p => p.player_id).filter(Boolean))) as number[]

    // Chunked IN fetch helper
    const fetchInChunks = async (table: string, col: string, ids: (string | number)[], selectCols: string): Promise<any[]> => {
      const out: any[] = []
      for (let i = 0; i < ids.length; i += CHUNK) {
        const slice = ids.slice(i, i + CHUNK)
        const { data, error } = await supabase.from(table).select(selectCols).in(col, slice as any)
        if (error) throw error
        out.push(...(data || []))
      }
      return out
    }

      // Fetch teams, conferences, and players
      const [teams, conferences, players] = await Promise.all([
      fetchInChunks('teams', 'team_id', teamIds, 'team_id, abbreviation, name, full_name'),
      fetchInChunks('conferences', 'conference_id', conferenceIds, 'conference_id, conference_name, conference_abbreviation'),
      fetchInChunks('players', 'player_id', playerIds, 'player_id, player_name, height, weight, birthday, pre_college')
    ])

      console.log(`Fetched ${teams.length} teams, ${conferences.length} conferences, ${players.length} players`)

      // Create lookup maps
      const teamMap = new Map(teams.map(t => [t.team_id, t]))
      const confMap = new Map(conferences.map(c => [c.conference_id, c]))
      const playerInfoMap = new Map(players.map(p => [p.player_id, p]))

      // Enhance player data with team, conference, and player info
      const enhancedPlayers = mergedPlayers.map(player => {
        const team = teamMap.get(player.team_id)
        const conference = confMap.get(player.conference_id)
        const playerInfo = playerInfoMap.get(player.player_id)

        return {
          ...player,
          // Add player info from players table
          player_name: playerInfo?.player_name || `Player ${player.player_id}`,
          height: playerInfo?.height || player.height,
          weight: playerInfo?.weight || player.weight,
          birthday: playerInfo?.birthday || null,
          pre_college: playerInfo?.pre_college || null,
          // Add team info
          team_abbreviation: team?.abbreviation || null,
          team_name: team?.name || null,
          team_full_name: team?.full_name || null,
          // Add conference info
          conference_name: conference?.conference_name || null,
          conference_abbreviation: conference?.conference_abbreviation || null,
          // Keep backward compatibility field names
          name: playerInfo?.player_name || `Player ${player.player_id}`,
          team: team?.name || null,
          conference: conference?.conference_name || null
        }
      })

      return NextResponse.json({ 
        players: enhancedPlayers,
        season,
        total: enhancedPlayers.length
      })
    }

    // Return single view data
    const singleViewData = Object.values(playerData)[0] as any[]
    return NextResponse.json({ 
      data: singleViewData,
      season,
      total: singleViewData?.length || 0
    })

  } catch (error) {
    console.error('Error in players API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch player data' },
      { status: 500 }
    )
  }
}
