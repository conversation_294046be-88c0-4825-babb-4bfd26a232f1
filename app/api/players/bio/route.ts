import { createClient } from '@supabase/supabase-js'
import { NextResponse } from 'next/server'

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabase = createClient(supabaseUrl, supabaseKey)

export async function GET(request: Request) {
  try {
    const url = new URL(request.url)
    const params = Object.fromEntries(url.searchParams.entries()) as {
      season?: string
    }

    console.log('Bio API called with params:', params)

    // Helper to fetch all rows with pagination
    const CHUNK = 1000
    const fetchAllFromView = async (): Promise<any[]> => {
      let offset = 0
      const all: any[] = []
      console.log('Fetching player_bio data...')
      
      // eslint-disable-next-line no-constant-condition
      while (true) {
        let query = supabase
          .from('player_bio')
          .select(`
            player_id, team_id, conference_id, season, position, role, class,
            number, pick, nba_team, transfer, height, weight, age, rec_rank
          `)
        
        if (params.season) query = query.eq('season', params.season)
        const { data, error } = await query.range(offset, offset + CHUNK - 1)
        
        if (error) {
          console.error('Error fetching player_bio chunk:', error)
          throw error
        }
        
        const batch = data || []
        all.push(...batch)
        console.log(`Fetched player_bio chunk ${offset}-${offset + batch.length - 1}, got ${batch.length} records`)
        
        if (batch.length < CHUNK) break
        offset += CHUNK
      }
      return all
    }

    const bioData = await fetchAllFromView()
    
    if (!bioData || bioData.length === 0) {
      console.log('No player_bio data found')
      return NextResponse.json({ data: [] })
    }

    // Collect player, team and conference IDs for additional data
    const playerIds = Array.from(new Set(bioData.map(p => p.player_id).filter(Boolean))) as number[]
    const teamIds = Array.from(new Set(bioData.map(p => p.team_id).filter(Boolean))) as number[]
    const conferenceIds = Array.from(new Set(bioData.map(p => p.conference_id).filter(Boolean))) as number[]

    // Chunked IN fetch helper
    const fetchInChunks = async (table: string, col: string, ids: (string | number)[], selectCols: string): Promise<any[]> => {
      const out: any[] = []
      for (let i = 0; i < ids.length; i += CHUNK) {
        const slice = ids.slice(i, i + CHUNK)
        const { data, error } = await supabase.from(table).select(selectCols).in(col, slice as any)
        if (error) throw error
        out.push(...(data || []))
      }
      return out
    }

    // Fetch players, teams and conferences
    const [players, teams, conferences] = await Promise.all([
      fetchInChunks('players', 'player_id', playerIds, 'player_id, player_name, height, weight, birthday, pre_college'),
      fetchInChunks('teams', 'team_id', teamIds, 'team_id, abbreviation, name, full_name'),
      fetchInChunks('conferences', 'conference_id', conferenceIds, 'conference_id, conference_name, conference_abbreviation')
    ])

    console.log(`Fetched ${players.length} players, ${teams.length} teams, ${conferences.length} conferences`)

    // Create lookup maps
    const playerMap = new Map(players.map(p => [p.player_id, p]))
    const teamMap = new Map(teams.map(t => [t.team_id, t]))
    const confMap = new Map(conferences.map(c => [c.conference_id, c]))

    // Enhance player data with player info, team and conference names
    const enhancedPlayers = bioData.map(player => {
      const playerInfo = playerMap.get(player.player_id)
      const team = teamMap.get(player.team_id)
      const conference = confMap.get(player.conference_id)
      
      return {
        ...player,
        player_name: playerInfo?.player_name || `Player ${player.player_id}`,
        birthday: playerInfo?.birthday || null,
        pre_college: playerInfo?.pre_college || null,
        team_abbreviation: team?.abbreviation || null,
        team_name: team?.name || null,
        team_full_name: team?.full_name || null,
        conference_name: conference?.conference_name || null,
        conference_abbreviation: conference?.conference_abbreviation || null,
        // Keep backward compatibility field names
        name: playerInfo?.player_name || `Player ${player.player_id}`,
        team: team?.name || null,
        conference: conference?.conference_name || null
      }
    })

    return NextResponse.json({ 
      data: enhancedPlayers,
      season: params.season,
      total: enhancedPlayers.length
    })

  } catch (error) {
    console.error('Error in bio API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch bio data' },
      { status: 500 }
    )
  }
} 