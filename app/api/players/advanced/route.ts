import { createClient } from '@supabase/supabase-js'
import { NextResponse } from 'next/server'

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabase = createClient(supabaseUrl, supabaseKey)

export async function GET(request: Request) {
  try {
    const url = new URL(request.url)
    const params = Object.fromEntries(url.searchParams.entries()) as {
      season?: string
    }

    console.log('Advanced stats API called with params:', params)

    // Helper to fetch all rows with pagination
    const CHUNK = 1000
    const fetchAllFromView = async (): Promise<any[]> => {
      let offset = 0
      const all: any[] = []
      console.log('Fetching player_advanced data...')
      
      // eslint-disable-next-line no-constant-condition
      while (true) {
        let query = supabase
          .from('player_advanced')
          .select(`
            player_id, season,
            min_pct, orb_pct, drb_pct, trb_pct,
            ast_pct, tov_pct, ast_tov,
            stl_pct, blk_pct, usg_pct,
            ppr, personal_foul_rate,
            ortg, drtg, ediff,
            fic, porpag, dporpag,
            adj_off_efficiency, adj_def_efficiency, stops, per,
            bpm, obpm, dbpm, gbpm, ogbpm, dgbpm
          `)
        
        if (params.season) query = query.eq('season', params.season)
        const { data, error } = await query.range(offset, offset + CHUNK - 1)
        
        if (error) {
          console.error('Error fetching player_advanced chunk:', error)
          throw error
        }
        
        const batch = data || []
        all.push(...batch)
        console.log(`Fetched player_advanced chunk ${offset}-${offset + batch.length - 1}, got ${batch.length} records`)
        
        if (batch.length < CHUNK) break
        offset += CHUNK
      }
      return all
    }

    const advancedData = await fetchAllFromView()
    
    if (!advancedData || advancedData.length === 0) {
      console.log('No player_advanced data found')
      return NextResponse.json({ data: [] })
    }

    // Collect player IDs for additional data
    const playerIds = Array.from(new Set(advancedData.map(p => p.player_id).filter(Boolean))) as number[]

    // Chunked IN fetch helper
    const fetchInChunks = async (table: string, col: string, ids: (string | number)[], selectCols: string): Promise<any[]> => {
      const out: any[] = []
      for (let i = 0; i < ids.length; i += CHUNK) {
        const slice = ids.slice(i, i + CHUNK)
        const { data, error } = await supabase.from(table).select(selectCols).in(col, slice as any)
        if (error) throw error
        out.push(...(data || []))
      }
      return out
    }

    // Fetch players
    const players = await fetchInChunks('players', 'player_id', playerIds, 'player_id, player_name, height, weight, birthday, pre_college')
    console.log(`Fetched ${players.length} players`)

    // Create lookup map
    const playerMap = new Map(players.map(p => [p.player_id, p]))

    // Enhance player data with player info
    const enhancedPlayers = advancedData.map(player => {
      const playerInfo = playerMap.get(player.player_id)
      
      return {
        ...player,
        player_name: playerInfo?.player_name || `Player ${player.player_id}`,
        name: playerInfo?.player_name || `Player ${player.player_id}`
      }
    })

    return NextResponse.json({ 
      data: enhancedPlayers,
      season: params.season,
      total: enhancedPlayers.length
    })

  } catch (error) {
    console.error('Error in advanced stats API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch advanced stats data' },
      { status: 500 }
    )
  }
} 