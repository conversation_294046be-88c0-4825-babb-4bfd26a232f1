"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>lider } from "@/components/ui/slider"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useIndex } from "@/components/index-provider"
import { SavedIndex } from "@/components/index-provider"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useRouter } from "next/navigation"
//

export default function CreateIndexPage() {
  const router = useRouter()
  const { statDefs, weights, setWeights, setUseCustomIndex, indexName, setIndexName, savedIndexes, saveIndex, deleteIndex, applySavedIndex } = useIndex()
  const [localWeights, setLocalWeights] = useState<Record<string, number>>(() => {
    const zeros: Record<string, number> = {}
    statDefs.forEach((def) => { (zeros as any)[def.key] = 0 })
    return zeros
  })
  const [localName, setLocalName] = useState(indexName)
  const [dupeOpen, setDupeOpen] = useState(false)

  const handleChange = (key: string, value: number) => {
    setLocalWeights((prev) => ({ ...prev, [key]: value }))
  }

  const handleSave = () => {
    const name = localName || "Custom Index"
    const exists = savedIndexes.some((i) => i.name.trim().toLowerCase() === name.trim().toLowerCase())
    if (exists) {
      setDupeOpen(true)
      return
    }
    setWeights(localWeights as any)
    setIndexName(name)
    setUseCustomIndex(true)
    saveIndex(name, localWeights as any)
    router.push("/")
  }

  const handleClear = () => {
    const zeros: Record<string, number> = {}
    statDefs.forEach((def) => { (zeros as any)[def.key] = 0 })
    setLocalWeights(zeros)
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-5xl">
      <div className="mb-6 flex items-center justify-between gap-2">
        <Button variant="outline" asChild className="mb-2">
          <Link href="/">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Link>
        </Button>
        <div className="flex items-center gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">My Indexes</Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>My Indexes</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                {savedIndexes.length === 0 && (
                  <div className="text-sm text-muted-foreground">No saved indexes yet.</div>
                )}
                {savedIndexes.map((idx: SavedIndex) => (
                  <div key={idx.name} className="rounded border p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-medium">{idx.name}</div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" onClick={() => { applySavedIndex(idx.name); router.push("/") }}>Apply</Button>
                        <Button size="sm" variant="destructive" onClick={() => deleteIndex(idx.name)}>Delete</Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                      {statDefs.map((def) => {
                        const w = (idx.weights as any)[def.key] ?? 0
                        if (!w) return null
                        return (
                          <div key={def.key} className="flex items-center justify-between">
                            <div className="text-muted-foreground">{def.label}</div>
                            <div className="font-medium">{w}</div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </DialogContent>
          </Dialog>
          {/* Top Save/Reset removed per requirements */}
        </div>
      </div>

      <h1 className="text-3xl font-bold mb-2">Create Custom Player Index</h1>
      <p className="text-muted-foreground mb-4">Assign weights 1-5 to each stat. Higher weight increases its influence. Negative-impact stats are accounted for automatically.</p>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Index Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid sm:grid-cols-1 gap-4">
            <div>
              <Label htmlFor="indexName">Index Name</Label>
              <Input id="indexName" value={localName} onChange={(e) => setLocalName(e.target.value)} placeholder="e.g. Shot Creator 2025" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Weights</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {statDefs.map((def) => {
              const key = def.key
              const value = (localWeights as any)[key] ?? 0
              return (
                <div key={key} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="font-medium">{def.label}</div>
                    <div className="text-sm text-muted-foreground">{value}</div>
                  </div>
                  <Slider
                    value={[value]}
                    onValueChange={(v: number[]) => handleChange(key, v[0])}
                    min={0}
                    max={5}
                    step={1}
                  />
                </div>
              )
            })}
          </div>
          <div className="flex justify-end gap-2 pt-2">
            <Button variant="outline" onClick={handleClear}>Clear</Button>
            <Button onClick={handleSave}>Save</Button>
            <Dialog open={dupeOpen} onOpenChange={setDupeOpen}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Replace existing index?</DialogTitle>
                </DialogHeader>
                <div className="space-y-3 text-sm">
                  <p>An index named "{localName || 'Custom Index'}" already exists. Do you want to replace it or continue editing?</p>
                  <div className="flex justify-end gap-2 pt-2">
                    <Button variant="outline" onClick={() => setDupeOpen(false)}>Continue Editing</Button>
                    <Button onClick={() => {
                      const name = localName || 'Custom Index'
                      setWeights(localWeights as any)
                      setIndexName(name)
                      setUseCustomIndex(true)
                      saveIndex(name, localWeights as any)
                      setDupeOpen(false)
                      router.push('/')
                    }}>Replace</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
