-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.


CREATE TABLE public.conferences (
  conference_id bigint NOT NULL,
  conference_name text,
  conference_abbreviation text,
  CONSTRAINT conferences_pkey PRIMARY KEY (conference_id)
);
CREATE TABLE public.player_info (
  player_id bigint NOT NULL,
  team_id bigint,
  conference_id bigint,
  season text NOT NULL,
  position text,
  role text,
  class text,
  number text,
  pick text,
  nba_team text,
  transfer boolean,
  height text,
  age bigint,
  height_inches bigint,
  weight bigint,
  gp bigint,
  mpg numeric,
  min_pct numeric,
  ppm numeric,
  apm numeric,
  rpm numeric,
  ppg numeric,
  fgm numeric,
  fga numeric,
  2pm numeric,
  2pa numeric,
  3pm numeric,
  3pa numeric,
  ftm numeric,
  fta numeric,
  orb numeric,
  drb numeric,
  rpg numeric,
  apg numeric,
  spg numeric,
  bpg numeric,
  tov numeric,
  pf numeric,
  fg_pct numeric,
  2p_pct numeric,
  3p_pct numeric,
  ft_pct numeric,
  rim_makes numeric,
  rim_attempts numeric,
  rim_pct numeric,
  mid_range_makes numeric,
  mid_range_attempts numeric,
  mid_range_pct numeric,
  dunks_makes numeric,
  dunks_attempts numeric,
  dunks_pct numeric,
  ftr numeric,
  ts_pct numeric,
  efg_pct numeric,
  total_s_pct numeric,
  orb_pct numeric,
  drb_pct numeric,
  trb_pct numeric,
  ast_pct numeric,
  tov_pct numeric,
  stl_pct numeric,
  blk_pct numeric,
  usg_pct numeric,
  ppr numeric,
  pps numeric,
  ast_tov numeric,
  personal_foul_rate numeric,
  3p_100 numeric,
  ortg numeric,
  drtg numeric,
  ediff numeric,
  porpag numeric,
  adj_off_efficiency numeric,
  adj_def_efficiency numeric,
  dporpag numeric,
  stops numeric,
  fic numeric,
  per numeric,
  rec_rank numeric,
  bpm numeric,
  obpm numeric,
  dbpm numeric,
  gbpm numeric,
  ogbpm numeric,
  dgbpm numeric,
  height_inches_z numeric,
  adj_pps numeric,
  cumulative_2pa numeric,
  cumulative_2pm numeric,
  cumulative_fta numeric,
  cumulative_ftm numeric,
  cumulative_3pa numeric,
  cumulative_3pm numeric,
  CONSTRAINT player_info_pkey PRIMARY KEY (player_id, season),
  CONSTRAINT fk_conference FOREIGN KEY (conference_id) REFERENCES public.conferences(conference_id),
  CONSTRAINT fk_player FOREIGN KEY (player_id) REFERENCES public.players(player_id),
  CONSTRAINT fk_team FOREIGN KEY (team_id) REFERENCES public.teams(team_id)
);
CREATE TABLE public.player_zscores (
  player_id bigint NOT NULL,
  season text NOT NULL,
  age_z numeric,
  height_inches_z numeric,
  weight_z numeric,
  gp_z numeric,
  mpg_z numeric,
  min_pct_z numeric,
  ppm_z numeric,
  apm_z numeric,
  rpm_z numeric,
  ppg_z numeric,
  fgm_z numeric,
  fga_z numeric,
  2pm_z numeric,
  2pa_z numeric,
  3pm_z numeric,
  3pa_z numeric,
  ftm_z numeric,
  fta_z numeric,
  orb_z numeric,
  drb_z numeric,
  rpg_z numeric,
  apg_z numeric,
  spg_z numeric,
  bpg_z numeric,
  tov_z numeric,
  pf_z numeric,
  fg_pct_z numeric,
  2p_pct_z numeric,
  3p_pct_z numeric,
  ft_pct_z numeric,
  rim_makes_z numeric,
  rim_attempts_z numeric,
  rim_pct_z numeric,
  mid_range_makes_z numeric,
  mid_range_attempts_z numeric,
  mid_range_pct_z numeric,
  dunks_makes_z numeric,
  dunks_attempts_z numeric,
  dunks_pct_z numeric,
  ftr_z numeric,
  ts_pct_z numeric,
  efg_pct_z numeric,
  total_s_pct_z numeric,
  orb_pct_z numeric,
  drb_pct_z numeric,
  trb_pct_z numeric,
  ast_pct_z numeric,
  tov_pct_z numeric,
  stl_pct_z numeric,
  blk_pct_z numeric,
  usg_pct_z numeric,
  ppr_z numeric,
  pps_z numeric,
  ast_tov_z numeric,
  personal_foul_rate_z numeric,
  3p_100_z numeric,
  ortg_z numeric,
  drtg_z numeric,
  ediff_z numeric,
  porpag_z numeric,
  adj_off_efficiency_z numeric,
  adj_def_efficiency_z numeric,
  dporpag_z numeric,
  stops_z numeric,
  fic_z numeric,
  per_z numeric,
  rec_rank_z numeric,
  bpm_z numeric,
  obpm_z numeric,
  dbpm_z numeric,
  gbpm_z numeric,
  ogbpm_z numeric,
  dgbpm_z numeric,
  cumulative_ftm_z numeric,
  cumulative_fta_z numeric,
  cumulative_2pm_z numeric,
  cumulative_2pa_z numeric,
  cumulative_3pm_z numeric,
  cumulative_3pa_z numeric,
  CONSTRAINT player_zscores_pkey PRIMARY KEY (player_id, season),
  CONSTRAINT player_zscores_player_id_fkey FOREIGN KEY (player_id) REFERENCES public.players(player_id)
);
CREATE TABLE public.player_zscores_role (
  player_id bigint NOT NULL,
  season text NOT NULL,
  age_z numeric,
  height_inches_z numeric,
  weight_z numeric,
  gp_z numeric,
  mpg_z numeric,
  min_pct_z numeric,
  ppm_z numeric,
  apm_z numeric,
  rpm_z numeric,
  ppg_z numeric,
  fgm_z numeric,
  fga_z numeric,
  2pm_z numeric,
  2pa_z numeric,
  3pm_z numeric,
  3pa_z numeric,
  ftm_z numeric,
  fta_z numeric,
  orb_z numeric,
  drb_z numeric,
  rpg_z numeric,
  apg_z numeric,
  spg_z numeric,
  bpg_z numeric,
  tov_z numeric,
  pf_z numeric,
  fg_pct_z numeric,
  2p_pct_z numeric,
  3p_pct_z numeric,
  ft_pct_z numeric,
  rim_makes_z numeric,
  rim_attempts_z numeric,
  rim_pct_z numeric,
  mid_range_makes_z numeric,
  mid_range_attempts_z numeric,
  mid_range_pct_z numeric,
  dunks_makes_z numeric,
  dunks_attempts_z numeric,
  dunks_pct_z numeric,
  ftr_z numeric,
  ts_pct_z numeric,
  efg_pct_z numeric,
  total_s_pct_z numeric,
  orb_pct_z numeric,
  drb_pct_z numeric,
  trb_pct_z numeric,
  ast_pct_z numeric,
  tov_pct_z numeric,
  stl_pct_z numeric,
  blk_pct_z numeric,
  usg_pct_z numeric,
  ppr_z numeric,
  pps_z numeric,
  ast_tov_z numeric,
  personal_foul_rate_z numeric,
  3p_100_z numeric,
  ortg_z numeric,
  drtg_z numeric,
  ediff_z numeric,
  porpag_z numeric,
  adj_off_efficiency_z numeric,
  adj_def_efficiency_z numeric,
  dporpag_z numeric,
  stops_z numeric,
  fic_z numeric,
  per_z numeric,
  rec_rank_z numeric,
  bpm_z numeric,
  obpm_z numeric,
  dbpm_z numeric,
  gbpm_z numeric,
  ogbpm_z numeric,
  dgbpm_z numeric,
  cumulative_ftm_z numeric,
  cumulative_fta_z numeric,
  cumulative_2pm_z numeric,
  cumulative_2pa_z numeric,
  cumulative_3pm_z numeric,
  cumulative_3pa_z numeric,
  CONSTRAINT player_zscores_role_pkey PRIMARY KEY (player_id, season),
  CONSTRAINT player_role_zscores_player_id_fkey FOREIGN KEY (player_id) REFERENCES public.players(player_id)
);
CREATE TABLE public.player_zscores_role_season (
  player_id bigint NOT NULL,
  season text NOT NULL,
  age_z numeric,
  height_inches_z numeric,
  weight_z numeric,
  gp_z numeric,
  mpg_z numeric,
  min_pct_z numeric,
  ppm_z numeric,
  apm_z numeric,
  rpm_z numeric,
  ppg_z numeric,
  fgm_z numeric,
  fga_z numeric,
  2pm_z numeric,
  2pa_z numeric,
  3pm_z numeric,
  3pa_z numeric,
  ftm_z numeric,
  fta_z numeric,
  orb_z numeric,
  drb_z numeric,
  rpg_z numeric,
  apg_z numeric,
  spg_z numeric,
  bpg_z numeric,
  tov_z numeric,
  pf_z numeric,
  fg_pct_z numeric,
  2p_pct_z numeric,
  3p_pct_z numeric,
  ft_pct_z numeric,
  rim_makes_z numeric,
  rim_attempts_z numeric,
  rim_pct_z numeric,
  mid_range_makes_z numeric,
  mid_range_attempts_z numeric,
  mid_range_pct_z numeric,
  dunks_makes_z numeric,
  dunks_attempts_z numeric,
  dunks_pct_z numeric,
  ftr_z numeric,
  ts_pct_z numeric,
  efg_pct_z numeric,
  total_s_pct_z numeric,
  orb_pct_z numeric,
  drb_pct_z numeric,
  trb_pct_z numeric,
  ast_pct_z numeric,
  tov_pct_z numeric,
  stl_pct_z numeric,
  blk_pct_z numeric,
  usg_pct_z numeric,
  ppr_z numeric,
  pps_z numeric,
  ast_tov_z numeric,
  personal_foul_rate_z numeric,
  3p_100_z numeric,
  ortg_z numeric,
  drtg_z numeric,
  ediff_z numeric,
  porpag_z numeric,
  adj_off_efficiency_z numeric,
  adj_def_efficiency_z numeric,
  dporpag_z numeric,
  stops_z numeric,
  fic_z numeric,
  per_z numeric,
  rec_rank_z numeric,
  bpm_z numeric,
  obpm_z numeric,
  dbpm_z numeric,
  gbpm_z numeric,
  ogbpm_z numeric,
  dgbpm_z numeric,
  cumulative_ftm_z numeric,
  cumulative_fta_z numeric,
  cumulative_2pm_z numeric,
  cumulative_2pa_z numeric,
  cumulative_3pm_z numeric,
  cumulative_3pa_z numeric,
  CONSTRAINT player_zscores_role_season_pkey PRIMARY KEY (player_id, season),
  CONSTRAINT playvision_role_season_zscores_player_id_fkey FOREIGN KEY (player_id) REFERENCES public.players(player_id)
);
CREATE TABLE public.player_zscores_season (
  player_id bigint NOT NULL,
  season text NOT NULL,
  age_z numeric,
  height_inches_z numeric,
  weight_z numeric,
  gp_z numeric,
  mpg_z numeric,
  min_pct_z numeric,
  ppm_z numeric,
  apm_z numeric,
  rpm_z numeric,
  ppg_z numeric,
  fgm_z numeric,
  fga_z numeric,
  2pm_z numeric,
  2pa_z numeric,
  3pm_z numeric,
  3pa_z numeric,
  ftm_z numeric,
  fta_z numeric,
  orb_z numeric,
  drb_z numeric,
  rpg_z numeric,
  apg_z numeric,
  spg_z numeric,
  bpg_z numeric,
  tov_z numeric,
  pf_z numeric,
  fg_pct_z numeric,
  2p_pct_z numeric,
  3p_pct_z numeric,
  ft_pct_z numeric,
  rim_makes_z numeric,
  rim_attempts_z numeric,
  rim_pct_z numeric,
  mid_range_makes_z numeric,
  mid_range_attempts_z numeric,
  mid_range_pct_z numeric,
  dunks_makes_z numeric,
  dunks_attempts_z numeric,
  dunks_pct_z numeric,
  ftr_z numeric,
  ts_pct_z numeric,
  efg_pct_z numeric,
  total_s_pct_z numeric,
  orb_pct_z numeric,
  drb_pct_z numeric,
  trb_pct_z numeric,
  ast_pct_z numeric,
  tov_pct_z numeric,
  stl_pct_z numeric,
  blk_pct_z numeric,
  usg_pct_z numeric,
  ppr_z numeric,
  pps_z numeric,
  ast_tov_z numeric,
  personal_foul_rate_z numeric,
  3p_100_z numeric,
  ortg_z numeric,
  drtg_z numeric,
  ediff_z numeric,
  porpag_z numeric,
  adj_off_efficiency_z numeric,
  adj_def_efficiency_z numeric,
  dporpag_z numeric,
  stops_z numeric,
  fic_z numeric,
  per_z numeric,
  rec_rank_z numeric,
  bpm_z numeric,
  obpm_z numeric,
  dbpm_z numeric,
  gbpm_z numeric,
  ogbpm_z numeric,
  dgbpm_z numeric,
  cumulative_ftm_z numeric,
  cumulative_fta_z numeric,
  cumulative_2pm_z numeric,
  cumulative_2pa_z numeric,
  cumulative_3pm_z numeric,
  cumulative_3pa_z numeric,
  CONSTRAINT player_zscores_season_pkey PRIMARY KEY (player_id, season),
  CONSTRAINT player_zscores_season_player_id_fkey FOREIGN KEY (player_id) REFERENCES public.players(player_id)
);
CREATE TABLE public.players (
  player_id bigint NOT NULL,
  player_name text,
  birthday text,
  height text,
  weight bigint,
  pre_college text,
  CONSTRAINT players_pkey PRIMARY KEY (player_id)
);
CREATE TABLE public.playvision_applications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  created_at timestamp with time zone DEFAULT now(),
  full_name character varying NOT NULL,
  email character varying NOT NULL,
  team_name character varying NOT NULL,
  role character varying NOT NULL,
  analytics_needs text NOT NULL,
  how_heard_about_us text,
  additional_info text,
  status character varying DEFAULT 'pending'::character varying,
  admin_notes text,
  assigned_to character varying,
  priority character varying DEFAULT 'normal'::character varying,
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT playvision_applications_pkey PRIMARY KEY (id)
);
CREATE TABLE public.scores (
  player_id bigint NOT NULL,
  season text NOT NULL,
  all_time_ppi numeric,
  all_time_poi numeric,
  all_time_pdi numeric,
  season_ppi numeric,
  season_poi numeric,
  season_pdi numeric,
  CONSTRAINT scores_pkey PRIMARY KEY (player_id, season)
);
CREATE TABLE public.team_info (
  team_id bigint NOT NULL,
  season text NOT NULL,
  conference_id bigint,
  division text,
  record text,
  conference_record text,
  gp bigint,
  mpg double precision,
  ppg double precision,
  fgm double precision,
  fga double precision,
  fg_pct double precision,
  3pm double precision,
  3pa double precision,
  3p_pct double precision,
  ftm double precision,
  fta double precision,
  ft_pct double precision,
  orb double precision,
  drb double precision,
  rpg double precision,
  apg double precision,
  spg double precision,
  bpg double precision,
  tov double precision,
  pf double precision,
  ts_pct double precision,
  efg_pct double precision,
  total_s_pct double precision,
  orb_pct double precision,
  drb_pct double precision,
  trb_pct double precision,
  ast_pct double precision,
  tov_pct double precision,
  stl_pct double precision,
  blk_pct double precision,
  pps double precision,
  fic40 double precision,
  ortg double precision,
  drtg double precision,
  ediff double precision,
  poss double precision,
  pace double precision,
  def_efg_pct double precision,
  ftr double precision,
  ftr_def double precision,
  def_to_pct double precision,
  2p_pct double precision,
  def_2p_pct double precision,
  def_ft_pct double precision,
  3p_rate double precision,
  3p_rate_def double precision,
  def_ast_pct double precision,
  blked_pct double precision,
  off_dunks_fg_pct double precision,
  off_dunk_share double precision,
  def_dunks_fg_pct double precision,
  def_dunk_share double precision,
  off_rim_fg_pct double precision,
  off_rim_share double precision,
  def_rim_fg_pct double precision,
  def_rim_share double precision,
  off_mid_range_fg_pct double precision,
  off_mid_range_share double precision,
  def_mid_range_fg_pct double precision,
  def_mid_range_share double precision,
  off_3p_share double precision,
  def_3p_fg_pct double precision,
  def_3p_share double precision,
  adj_off_efficiency double precision,
  adj_def_efficiency double precision,
  barthag double precision,
  strength_of_schedule_sos double precision,
  non_conference_sos double precision,
  conference_sos double precision,
  opp_oe double precision,
  opp_de double precision,
  gp_z double precision,
  mpg_z double precision,
  ppg_z double precision,
  fgm_z double precision,
  fga_z double precision,
  fg_pct_z double precision,
  3pm_z double precision,
  3pa_z double precision,
  3p_pct_z double precision,
  ftm_z double precision,
  fta_z double precision,
  ft_pct_z double precision,
  orb_z double precision,
  drb_z double precision,
  rpg_z double precision,
  apg_z double precision,
  spg_z double precision,
  bpg_z double precision,
  tov_z double precision,
  pf_z double precision,
  ts_pct_z double precision,
  efg_pct_z double precision,
  total_s_pct_z double precision,
  orb_pct_z double precision,
  drb_pct_z double precision,
  trb_pct_z double precision,
  ast_pct_z double precision,
  tov_pct_z double precision,
  stl_pct_z double precision,
  blk_pct_z double precision,
  pps_z double precision,
  fic40_z double precision,
  ortg_z double precision,
  drtg_z double precision,
  ediff_z double precision,
  poss_z double precision,
  pace_z double precision,
  def_efg_pct_z double precision,
  ftr_z double precision,
  ftr_def_z double precision,
  def_to_pct_z double precision,
  2p_pct_z double precision,
  def_2p_pct_z double precision,
  def_ft_pct_z double precision,
  3p_rate_z double precision,
  3p_rate_def_z double precision,
  def_ast_pct_z double precision,
  blked_pct_z double precision,
  off_dunks_fg_pct_z double precision,
  off_dunk_share_z double precision,
  def_dunks_fg_pct_z double precision,
  def_dunk_share_z double precision,
  off_rim_fg_pct_z double precision,
  off_rim_share_z double precision,
  def_rim_fg_pct_z double precision,
  def_rim_share_z double precision,
  off_mid_range_fg_pct_z double precision,
  off_mid_range_share_z double precision,
  def_mid_range_fg_pct_z double precision,
  def_mid_range_share_z double precision,
  off_3p_share_z double precision,
  def_3p_fg_pct_z double precision,
  def_3p_share_z double precision,
  adj_off_efficiency_z double precision,
  adj_def_efficiency_z double precision,
  barthag_z double precision,
  strength_of_schedule_sos_z double precision,
  non_conference_sos_z double precision,
  conference_sos_z double precision,
  opp_oe_z double precision,
  opp_de_z double precision,
  CONSTRAINT team_info_pkey PRIMARY KEY (team_id, season),
  CONSTRAINT fk_team FOREIGN KEY (team_id) REFERENCES public.teams(team_id),
  CONSTRAINT fk_conference FOREIGN KEY (conference_id) REFERENCES public.conferences(conference_id)
);
CREATE TABLE public.teams (
  team_id bigint NOT NULL,
  abbreviation text,
  name text,
  full_name text,
  CONSTRAINT teams_pkey PRIMARY KEY (team_id)
);