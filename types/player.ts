export type Position = "PG" | "SG" | "SF" | "PF" | "C" | "F" | "G" | "GF" | "FC"

export interface PlayerStats {
  gp?: number
  mpg?: number
  min_pct?: number
  ppm?: number
  apm?: number
  rpm?: number
  ppg?: number
  fgm?: number
  fga?: number
  fg_pct?: number
  '2pm'?: number
  '2pa'?: number
  '2p_pct'?: number
  '3pm'?: number
  '3pa'?: number
  '3p_pct'?: number
  ftm?: number
  fta?: number
  ft_pct?: number
  orb?: number
  drb?: number
  rpg?: number
  apg?: number
  spg?: number
  bpg?: number
  tov?: number
  pf?: number
}

export interface AdvancedStats {
  rim_makes?: number
  rim_attempts?: number
  rim_pct?: number
  mid_range_makes?: number
  mid_range_attempts?: number
  mid_range_pct?: number
  dunks_makes?: number
  dunks_attempts?: number
  dunks_pct?: number
  ftr?: number
  ts_pct?: number
  efg_pct?: number
  total_s_pct?: number
  orb_pct?: number
  drb_pct?: number
  trb_pct?: number
  ast_pct?: number
  tov_pct?: number
  stl_pct?: number
  blk_pct?: number
  usg_pct?: number
  ppr?: number
  pps?: number
  ast_tov?: number
  personal_foul_rate?: number
  '3p_100'?: number
  ortg?: number
  drtg?: number
  ediff?: number
  porpag?: number
  adj_off_efficiency?: number
  adj_def_efficiency?: number
  dporpag?: number
  stops?: number
  fic?: number
  per?: number
  rec_rank?: number
  bpm?: number
  obpm?: number
  dbpm?: number
  gbpm?: number
  ogbpm?: number
  dgbpm?: number
  adj_pps?: number
}

export interface PlayerScores {
  all_time_ppi?: number
  all_time_poi?: number
  all_time_pdi?: number
  season_ppi?: number
  season_poi?: number
  season_pdi?: number
}

export interface PlayerZScore {
  age_z?: number
  height_inches_z?: number
  weight_z?: number
  gp_z?: number
  mpg_z?: number
  min_pct_z?: number
  ppm_z?: number
  apm_z?: number
  rpm_z?: number
  ppg_z?: number
  fgm_z?: number
  fga_z?: number
  '2pm_z'?: number
  '2pa_z'?: number
  '3pm_z'?: number
  '3pa_z'?: number
  ftm_z?: number
  fta_z?: number
  orb_z?: number
  drb_z?: number
  rpg_z?: number
  apg_z?: number
  spg_z?: number
  bpg_z?: number
  tov_z?: number
  pf_z?: number
  fg_pct_z?: number
  '2p_pct_z'?: number
  '3p_pct_z'?: number
  ft_pct_z?: number
  rim_makes_z?: number
  rim_attempts_z?: number
  rim_pct_z?: number
  mid_range_makes_z?: number
  mid_range_attempts_z?: number
  mid_range_pct_z?: number
  dunks_makes_z?: number
  dunks_attempts_z?: number
  dunks_pct_z?: number
  ftr_z?: number
  ts_pct_z?: number
  efg_pct_z?: number
  total_s_pct_z?: number
  orb_pct_z?: number
  drb_pct_z?: number
  trb_pct_z?: number
  ast_pct_z?: number
  tov_pct_z?: number
  stl_pct_z?: number
  blk_pct_z?: number
  usg_pct_z?: number
  ppr_z?: number
  pps_z?: number
  ast_tov_z?: number
  personal_foul_rate_z?: number
  '3p_100_z'?: number
  ortg_z?: number
  drtg_z?: number
  ediff_z?: number
  porpag_z?: number
  adj_off_efficiency_z?: number
  adj_def_efficiency_z?: number
  dporpag_z?: number
  stops_z?: number
  fic_z?: number
  per_z?: number
  rec_rank_z?: number
  bpm_z?: number
  obpm_z?: number
  dbpm_z?: number
  gbpm_z?: number
  ogbpm_z?: number
  dgbpm_z?: number
}

export interface Player {
  player_id: number
  team_id?: number
  conference_id?: number
  season: string
  position?: Position
  role?: string
  class?: string
  number?: string
  pick?: string
  nba_team?: string
  transfer?: boolean
  height?: string
  age?: number
  height_inches?: number
  weight?: number
  
  // Basic stats
  stats: PlayerStats
  
  // Advanced stats
  advancedStats?: AdvancedStats
  
  // Player scores (PPI, POI, PDI)
  scores?: PlayerScores
  
  // Z-scores
  zScores?: PlayerZScore
  
  // Player info from players table
  player_name?: string
  birthday?: string
  pre_college?: string
  
  // Team info from teams table
  team_abbreviation?: string
  team_name?: string
  team_full_name?: string
  
  // Conference info from conferences table
  conference_name?: string
  conference_abbreviation?: string
  
  // Legacy fields for backward compatibility
  id?: string | number
  name?: string
  playerIndex?: number
  active?: boolean
  starred?: boolean
  transferPortal?: boolean
  status?: string
  dateEntered?: string
  team?: string
  conference?: string
  notRated?: boolean
}

// Legacy interface for backward compatibility
export interface LegacyPlayer {
  id: string | number
  name: string
  number: number
  position: Position
  playerIndex: number
  height: string
  weight: number
  active: boolean
  starred?: boolean
  transferPortal?: boolean
  status?: string
  dateEntered?: string
  team: string
  conference?: string
  notRated?: boolean
  class?: string
  stats: {
    gp?: number
    mpg?: number
    ppg: number
    fgm?: number
    fga?: number
    fg?: number
    tpm?: number
    tpa?: number
    tpp?: number
    ftm?: number
    fta?: number
    ftp?: number
    orb?: number
    drb?: number
    rpg: number
    apg: number
    spg: number
    bpg: number
    tov?: number
    pf?: number
  }
  advancedStats?: AdvancedStats
}