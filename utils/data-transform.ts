// Import player types

import { Player, Position } from '@/types/player'

// Legacy shape kept for backward compatibility
interface RawLegacyPlayerData {
  ID: number
  Player: string
  Team: string
  Position: string
  Height: string
  Weight: number
  Conference: string
  GP: number
  MPG: number
  PPG: number
  FGM: number
  FGA: number
  'FG%': number
  '3PM': number
  '3PA': number
  '3P%': number
  FTM: number
  FTA: number
  'FT%': number
  ORB: number
  DRB: number
  RPG: number
  APG: number
  SPG: number
  BPG: number
  TOV: number
  PF: number
  'TS%': number
  'eFG%': number
  'Total S%': number
  'ORB%': number
  'DRB%': number
  'TRB%': number
  'AST%': number
  'TOV%': number
  'STL%': number
  'BLK%': number
  'USG%': number
  PPR: number
  PPS: number
  ORtg: number
  DRtg: number
  eDiff: number
  FIC: number
  PER: number
  FTRATE: number
  Height_Inches: number
  PPM: number
  RPM: number
  APM: number
  'Date Entered': string
  Status: string
  Combined_Stat: number
  'PlayVision Player Index': number
  POI?: number
  PDI?: number
  PEI?: number
  PUI?: number
  Class?: string
}

function isValidPosition(pos: string): pos is Position {
  return ['PG', 'SG', 'SF', 'PF', 'C', 'F', 'G', 'GF', 'FC'].includes(pos)
}

function percentTo100(value?: number): number {
  if (value === undefined || value === null) return 0
  const n = Number(value)
  if (isNaN(n)) return 0
  return n <= 1 ? n * 100 : n
}

// NEW SCHEMA transformer
function transformFromNewSchema(rows: any[]): Player[] {
  return rows.map((row) => {
    const pos = (row.position as string) || 'G'
    const tName = row.team_full_name || row.team_name || row.team_abbreviation || ''
    const rpg = Number(row.rpg ?? (Number(row.drb || 0) + Number(row.orb || 0))) || 0

    const player: Player = {
      player_id: row.player_id,
      season: row.season || 'Unknown',
      id: row.player_id,
      name: row.player_name || 'Unknown',
      number: row.number || '',
      position: isValidPosition(pos) ? (pos as Position) : 'G',
      playerIndex: 0,
      height: row.height || '',
      weight: Number(row.weight) || 0,
      active: true,
      team: tName,
      conference: row.conference || row.conference_abbreviation,
      class: row.class || undefined,
      stats: {
        gp: Number(row.gp) || 0,
        mpg: Number(row.mpg) || 0,
        ppg: Number(row.ppg) || 0,
        fgm: Number(row.fgm) || 0,
        fga: Number(row.fga) || 0,
        fg_pct: Number(row.fg_pct) || 0,
        '3pm': Number(row['3pm']) || 0,
        '3pa': Number(row['3pa']) || 0,
        '3p_pct': Number(row['3p_pct']) || 0,
        ftm: Number(row.ftm) || 0,
        fta: Number(row.fta) || 0,
        ft_pct: Number(row.ft_pct) || 0,
        orb: Number(row.orb) || 0,
        drb: Number(row.drb) || 0,
        rpg,
        apg: Number(row.apg) || 0,
        spg: Number(row.spg) || 0,
        bpg: Number(row.bpg) || 0,
        tov: Number(row.tov) || 0,
        pf: Number(row.pf) || 0,
      },
    }

    return player
  })
}

// LEGACY transformer
function transformFromLegacy(rawData: RawLegacyPlayerData[]): Player[] {
  try {
    return rawData.map((row) => {
      const position = row.Position || 'G'

      return {
        player_id: row.ID,
        season: 'Legacy',
        id: row.ID,
        name: row.Player || 'Unknown',
        number: '', // Default jersey number
        position: isValidPosition(position) ? position : 'G',
        height: row.Height || '',
        weight: Number(row.Weight) || 0,
        active: row.Status?.toLowerCase() !== 'inactive',
        status: row.Status || undefined,
        dateEntered: row['Date Entered'] || undefined,
        transferPortal: row.Status === 'Entered' || row.Status === 'Committed',
        team: row.Team || 'Unknown',
        conference: row.Conference || undefined,
        playerIndex: Number(row['PlayVision Player Index']) || 0,
        notRated: Number(row['PlayVision Player Index']) === 0,
        class: row.Class || undefined,
        stats: {
          gp: Number(row.GP) || 0,
          mpg: Number(row.MPG) || 0,
          ppg: Number(row.PPG) || 0,
          fgm: Number(row.FGM) || 0,
          fga: Number(row.FGA) || 0,
          fg: Number(row['FG%']) * 100 || 0,
          tpm: Number(row['3PM']) || 0,
          tpa: Number(row['3PA']) || 0,
          tpp: Number(row['3P%']) * 100 || 0,
          ftm: Number(row.FTM) || 0,
          fta: Number(row.FTA) || 0,
          ftp: Number(row['FT%']) * 100 || 0,
          orb: Number(row.ORB) || 0,
          drb: Number(row.DRB) || 0,
          rpg: Number(row.RPG) || 0,
          apg: Number(row.APG) || 0,
          spg: Number(row.SPG) || 0,
          bpg: Number(row.BPG) || 0,
          tov: Number(row.TOV) || 0,
          pf: Number(row.PF) || 0
        },
        advancedStats: {
          // Advanced stats from the raw data
          ts: Number(row['TS%']) * 100 || 0,      // TS%
          efg: Number(row['eFG%']) * 100 || 0,     // eFG%
          totalS: Number(row['Total S%']) || 0,  // Total S%
          orb: Number(row['ORB%']) || 0,     // ORB%
          drb: Number(row['DRB%']) || 0,     // DRB%
          trb: Number(row['TRB%'])|| 0,     // TRB%
          ast: Number(row['AST%']) || 0,     // AST%
          tov: Number(row['TOV%']) || 0,     // TOV%
          stl: Number(row['STL%']) || 0,     // STL%
          blk: Number(row['BLK%']) || 0,     // BLK%
          usg: Number(row['USG%']) || 0,     // USG%
          ppr: Number(row.PPR) || 0,     // PPR
          pps: Number(row.PPS) || 0,     // PPS
          ortg: Number(row.ORtg) || 0,    // ORtg
          drtg: Number(row.DRtg) || 0,    // DRtg
          ediff: Number(row.eDiff) || 0,   // eDiff
          fic: Number(row.FIC) || 0,     // FIC
          per: Number(row.PER) || 0,     // PER
          ftrate: Number(row.FTRATE) * 100 || 0,  // FTRATE
          poi: Number(row.POI) || 0,     // POI
          pdi: Number(row.PDI) || 0,     // PDI
          pei: Number(row.PEI) || 0,     // PEI
          pui: Number(row.PUI) || 0      // PUI
        }
      }
    })
  } catch (error) {
    console.error('Error in transformFromLegacy:', error)
    return []
  }
}

export interface PaginationInfo {
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface PlayerDataResponse {
  players: any[]
  pagination: PaginationInfo
}

interface PlayerDataParams {
  page?: number
  pageSize?: number
  season?: string
  position?: string
  searchTerm?: string
  status?: string
  [key: string]: any
}

export async function getPlayerData(params: PlayerDataParams = {}): Promise<PlayerDataResponse> {
  try {
    console.log('Fetching player data from API with params:', params)

    // Build query string from params
    const queryParams = new URLSearchParams()

    // Add all params to query string
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString())
      }
    })

    // Build absolute URL when on the server to avoid base path issues
    const origin = typeof window === 'undefined'
      ? (process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000')
      : ''
    const url = `${origin}/api/players?${queryParams.toString()}`

    // Make the API request
    const response = await fetch(url, { cache: 'no-store' })

    if (!response.ok) {
      console.error('API fetch failed:', response.status, response.statusText)
      return {
        players: [],
        pagination: { total: 0, page: 1, pageSize: 10, totalPages: 0 }
      }
    }

    const result = await response.json()
    console.log('API data received:', result)

    // Handle new API response structure
    let transformedData: any[]
    
    if (result.players) {
      // New comprehensive response structure - needs stats restructuring
      console.log('Using new comprehensive data structure')
      console.log('First player sample before transformation:', {
        player_id: result.players[0]?.player_id,
        name: result.players[0]?.name,
        player_name: result.players[0]?.player_name,
        team: result.players[0]?.team,
        ppg: result.players[0]?.ppg
      })
      transformedData = result.players.map((player: any) => ({
        ...player,
        // Ensure name is preserved
        name: player.player_name || 'Unknown Player',
        stats: {
          gp: player.gp,
          mpg: player.mpg,
          ppg: player.ppg,
          fgm: player.fgm,
          fga: player.fga,
          fg_pct: player.fg_pct,
          '3pm': player['3pm'],
          '3pa': player['3pa'],
          '3p_pct': player['3p_pct'],
          ftm: player.ftm,
          fta: player.fta,
          ft_pct: player.ft_pct,
          orb: player.orb,
          drb: player.drb,
          rpg: player.rpg,
          apg: player.apg,
          spg: player.spg,
          bpg: player.bpg,
          tov: player.tov,
          pf: player.pf,
        },
        advancedStats: {
          rim_makes: player.rim_makes,
          rim_attempts: player.rim_attempts,
          rim_pct: player.rim_pct,
          mid_range_makes: player.mid_range_makes,
          mid_range_attempts: player.mid_range_attempts,
          mid_range_pct: player.mid_range_pct,
          dunks_makes: player.dunks_makes,
          dunks_attempts: player.dunks_attempts,
          dunks_pct: player.dunks_pct,
          ftr: player.ftr,
          ts_pct: player.ts_pct,
          efg_pct: player.efg_pct,
          total_s_pct: player.total_s_pct,
          orb_pct: player.orb_pct,
          drb_pct: player.drb_pct,
          trb_pct: player.trb_pct,
          ast_pct: player.ast_pct,
          tov_pct: player.tov_pct,
          stl_pct: player.stl_pct,
          blk_pct: player.blk_pct,
          usg_pct: player.usg_pct,
          ppr: player.ppr,
          pps: player.pps,
          ast_tov: player.ast_tov,
          personal_foul_rate: player.personal_foul_rate,
          '3p_100': player['3p_100'],
          ortg: player.ortg,
          drtg: player.drtg,
          ediff: player.ediff,
          porpag: player.porpag,
          adj_off_efficiency: player.adj_off_efficiency,
          adj_def_efficiency: player.adj_def_efficiency,
          dporpag: player.dporpag,
          stops: player.stops,
          fic: player.fic,
          per: player.per,
          rec_rank: player.rec_rank,
          bpm: player.bpm,
          obpm: player.obpm,
          dbpm: player.dbpm,
          gbpm: player.gbpm,
          ogbpm: player.ogbpm,
          dgbpm: player.dgbpm,
                 }
       }))
      console.log('First player sample after transformation:', {
        player_id: transformedData[0]?.player_id,
        name: transformedData[0]?.name,
        player_name: transformedData[0]?.player_name,
        team: transformedData[0]?.team,
        ppg: transformedData[0]?.stats?.ppg
      })
    } else if (result.data) {
      // Legacy response structure or single view data
      const first = result.data[0]
      if (first && (first.player_name !== undefined || first.season !== undefined)) {
        // New schema - use data directly without transformation
        console.log('Using new schema data directly')
        transformedData = result.data
      } else {
        // Legacy schema - apply transformation
        console.log('Using legacy schema transformation')
        transformedData = transformFromLegacy(result.data as RawLegacyPlayerData[])
      }
    } else {
      // Fallback
      transformedData = []
    }

    console.log('Player data sample:', transformedData[0])

    // Calculate pagination info
    const page = params.page || 1
    const pageSize = params.pageSize || transformedData.length
    const total = transformedData.length
    const totalPages = Math.ceil(total / pageSize)

    return {
      players: transformedData,
      pagination: {
        total,
        page,
        pageSize,
        totalPages
      }
    }
  } catch (error) {
    console.error('Error in getPlayerData:', error)
    return {
      players: [],
      pagination: {
        total: 0,
        page: 1,
        pageSize: 10,
        totalPages: 0
      }
    }
  }
}