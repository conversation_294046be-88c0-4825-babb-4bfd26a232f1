import { Player } from "@/types/player"

export type StatKey =
  | "stats.ppg"
  | "stats.apg"
  | "stats.rpg"
  | "stats.spg"
  | "stats.bpg"
  | "stats.tov" // lower is better
  | "stats.fg_pct" // %
  | "stats.3p_pct" // 3P%
  | "stats.ft_pct" // FT%
  | "advanced.ts_pct"
  | "advanced.efg_pct"
  | "advanced.total_s_pct"
  | "advanced.orb_pct"
  | "advanced.drb_pct"
  | "advanced.trb_pct"
  | "advanced.ast_pct"
  | "advanced.tov_pct" // lower is better
  | "advanced.usg_pct"
  | "advanced.ppr"
  | "advanced.pps"
  | "advanced.ortg"
  | "advanced.drtg" // lower is better
  | "advanced.ediff"
  | "advanced.fic"
  | "advanced.per"
  | "advanced.ftr"

export interface StatDefinition {
  key: StatKey
  label: string
  higherIsBetter: boolean
  // Extract raw numeric value from player
  accessor: (p: Player) => number
}

export const STAT_DEFS: StatDefinition[] = [
  { key: "stats.ppg", label: "PPG", higherIsBetter: true, accessor: (p) => p.stats?.ppg ?? 0 },
  { key: "stats.apg", label: "APG", higherIsBetter: true, accessor: (p) => p.stats?.apg ?? 0 },
  { key: "stats.rpg", label: "RPG", higherIsBetter: true, accessor: (p) => p.stats?.rpg ?? 0 },
  { key: "stats.spg", label: "SPG", higherIsBetter: true, accessor: (p) => p.stats?.spg ?? 0 },
  { key: "stats.bpg", label: "BPG", higherIsBetter: true, accessor: (p) => p.stats?.bpg ?? 0 },
  { key: "stats.tov", label: "TOV", higherIsBetter: false, accessor: (p) => p.stats?.tov ?? 0 },
  { key: "stats.fg_pct", label: "FG%", higherIsBetter: true, accessor: (p) => p.stats?.fg_pct ?? 0 },
  { key: "stats.3p_pct", label: "3P%", higherIsBetter: true, accessor: (p) => p.stats?.['3p_pct'] ?? 0 },
  { key: "stats.ft_pct", label: "FT%", higherIsBetter: true, accessor: (p) => p.stats?.ft_pct ?? 0 },

  { key: "advanced.ts_pct", label: "TS%", higherIsBetter: true, accessor: (p) => p.advancedStats?.ts_pct ?? 0 },
  { key: "advanced.efg_pct", label: "eFG%", higherIsBetter: true, accessor: (p) => p.advancedStats?.efg_pct ?? 0 },
  { key: "advanced.total_s_pct", label: "Total S%", higherIsBetter: true, accessor: (p) => p.advancedStats?.total_s_pct ?? 0 },
  { key: "advanced.orb_pct", label: "ORB%", higherIsBetter: true, accessor: (p) => p.advancedStats?.orb_pct ?? 0 },
  { key: "advanced.drb_pct", label: "DRB%", higherIsBetter: true, accessor: (p) => p.advancedStats?.drb_pct ?? 0 },
  { key: "advanced.trb_pct", label: "TRB%", higherIsBetter: true, accessor: (p) => p.advancedStats?.trb_pct ?? 0 },
  { key: "advanced.ast_pct", label: "AST%", higherIsBetter: true, accessor: (p) => p.advancedStats?.ast_pct ?? 0 },
  { key: "advanced.tov_pct", label: "TOV%", higherIsBetter: false, accessor: (p) => p.advancedStats?.tov_pct ?? 0 },
  { key: "advanced.usg_pct", label: "USG%", higherIsBetter: true, accessor: (p) => p.advancedStats?.usg_pct ?? 0 },
  { key: "advanced.ppr", label: "PPR", higherIsBetter: true, accessor: (p) => p.advancedStats?.ppr ?? 0 },
  { key: "advanced.pps", label: "PPS", higherIsBetter: true, accessor: (p) => p.advancedStats?.pps ?? 0 },
  { key: "advanced.ortg", label: "ORtg", higherIsBetter: true, accessor: (p) => p.advancedStats?.ortg ?? 0 },
  { key: "advanced.drtg", label: "DRtg", higherIsBetter: false, accessor: (p) => p.advancedStats?.drtg ?? 0 },
  { key: "advanced.ediff", label: "eDiff", higherIsBetter: true, accessor: (p) => p.advancedStats?.ediff ?? 0 },
  { key: "advanced.fic", label: "FIC", higherIsBetter: true, accessor: (p) => p.advancedStats?.fic ?? 0 },
  { key: "advanced.per", label: "PER", higherIsBetter: true, accessor: (p) => p.advancedStats?.per ?? 0 },
  { key: "advanced.ftr", label: "FTR", higherIsBetter: true, accessor: (p) => p.advancedStats?.ftr ?? 0 },
]

export type IndexWeights = Partial<Record<StatKey, number>>

function computeMinMax(players: Player[], accessor: (p: Player) => number) {
  let min = Number.POSITIVE_INFINITY
  let max = Number.NEGATIVE_INFINITY
  for (const p of players) {
    const v = accessor(p)
    if (Number.isFinite(v)) {
      if (v < min) min = v
      if (v > max) max = v
    }
  }
  if (!Number.isFinite(min)) min = 0
  if (!Number.isFinite(max)) max = 0
  return { min, max }
}

function normalize(value: number, min: number, max: number, higherIsBetter: boolean): number {
  if (!Number.isFinite(value)) return 0
  if (max === min) return 0.5 // neutral when no spread
  const ratio = (value - min) / (max - min)
  const normalized = Math.max(0, Math.min(1, ratio))
  return higherIsBetter ? normalized : 1 - normalized
}

export function computeCustomIndexForAll(players: Player[], weights: IndexWeights): Player[] {
  const activeEntries = Object.entries(weights).filter(([, w]) => (w ?? 0) > 0) as [StatKey, number][]
  if (activeEntries.length === 0) {
    return players.map((p) => ({ ...p }))
  }

  // Pre-compute min/max per stat for normalization
  const minMaxByKey: Record<StatKey, { min: number; max: number; higherIsBetter: boolean; accessor: (p: Player) => number }> = {} as any
  for (const def of STAT_DEFS) {
    if (weights[def.key] && weights[def.key]! > 0) {
      const { min, max } = computeMinMax(players, def.accessor)
      minMaxByKey[def.key] = { min, max, higherIsBetter: def.higherIsBetter, accessor: def.accessor }
    }
  }

  const maxPossibleWeight = activeEntries.reduce((sum, [, w]) => sum + w, 0)

  return players.map((p) => {
    let score01 = 0
    for (const [key, w] of activeEntries) {
      const meta = minMaxByKey[key]
      if (!meta) continue
      const raw = meta.accessor(p)
      const n = normalize(raw, meta.min, meta.max, meta.higherIsBetter)
      score01 += (w / maxPossibleWeight) * n
    }
    // Scale to 0–95 with one decimal
    const score95 = Math.round(score01 * 950) / 10
    return { ...p, playerIndex: score95 }
  })
}

export function defaultWeights(): IndexWeights {
  // start with some reasonable defaults focused on core stats
  return {
    "stats.ppg": 3,
    "stats.apg": 3,
    "stats.rpg": 3,
    "stats.spg": 2,
    "stats.bpg": 2,
    "stats.fg_pct": 3,
    "stats.3p_pct": 3,
    "advanced.ts_pct": 3,
    "advanced.efg_pct": 2,
    "advanced.ortg": 3,
    "advanced.per": 3,
  }
}


